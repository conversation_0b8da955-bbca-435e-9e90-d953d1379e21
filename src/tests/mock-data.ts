import { ISearchData } from '../app/core/interface/search-base.interface';

export const MOCK_PRICING_DATA: ISearchData[] = [
  {
    id: 1,
    document: 'Land Index Search',
    address: '42 Sydney Road, Melbourne VIC 3000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 2,
    document: 'Title Search',
    address: '15 Collins Street, Melbourne VIC 3000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 3,
    document: 'Prior Title Search',
    address: '78 Elizabeth Street, Sydney NSW 2000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 4,
    document: 'Land Index Search',
    address: '25 George Street, Brisbane QLD 4000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 5,
    document: 'Land Index Search',
    address: '18 Murray Street, Perth WA 6000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 6,
    document: 'Land Index Search',
    address: '56 Rundle Mall, Adelaide SA 5000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 7,
    document: 'Land Index Search',
    address: '92 Swanston Street, Melbourne VIC 3000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 8,
    document: 'Plan Search',
    address: '105 Macquarie Street, Hobart TAS 7000',
    price: '$12.50 AUD',
    gst: '$12.50 AUD',
  },
  {
    id: 9,
    document: 'Power of Attorney Search',
    address: '33 Darwin Avenue, Darwin NT 0800',
    price: '$8.75 AUD',
    gst: '$8.75 AUD',
  },
  {
    id: 10,
    document: 'Document Retrieval',
    address: '5 London Circuit, Canberra ACT 2601',
    price: '$15.00 AUD',
    gst: '$15.00 AUD',
  },
  {
    id: 11,
    document: 'Property Certificate',
    address: '65 Flinders Lane, Melbourne VIC 3000',
    price: '$22.35 AUD',
    gst: '$22.35 AUD',
  },
  {
    id: 12,
    document: 'Company Search',
    address: '220 Pitt Street, Sydney NSW 2000',
    price: '$9.50 AUD',
    gst: '$9.50 AUD',
  },
  {
    id: 13,
    document: 'Historical Title Search',
    address: '47 Queen Street, Brisbane QLD 4000',
    price: '$14.25 AUD',
    gst: '$14.25 AUD',
  },
  {
    id: 14,
    document: 'Caveat Search',
    address: '81 Barrack Street, Perth WA 6000',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
  {
    id: 15,
    document: 'Water Access License Search',
    address: '120 King William Street, Adelaide SA 5000',
    price: '$18.90 AUD',
    gst: '$18.90 AUD',
  },
  {
    id: 16,
    document: 'Easement Search',
    address: '29 Liverpool Street, Hobart TAS 7000',
    price: '$7.85 AUD',
    gst: '$7.85 AUD',
  },
  {
    id: 17,
    document: 'Strata Plan Search',
    address: '72 Mitchell Street, Darwin NT 0800',
    price: '$11.30 AUD',
    gst: '$11.30 AUD',
  },
  {
    id: 18,
    document: 'Dealing Search',
    address: '54 Northbourne Avenue, Canberra ACT 2601',
    price: '$6.45 AUD',
    gst: '$6.45 AUD',
  },
];
