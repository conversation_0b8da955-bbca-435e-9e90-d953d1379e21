import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';
import { KeycloakService } from './app/core/services/keycloak.service';
import { provideAnimations } from '@angular/platform-browser/animations';

const keycloakService = new KeycloakService();

keycloakService
  .init()
  .then(() => {
    // Always bootstrap the app, even if unauthenticated
    bootstrapApplication(AppComponent, {
      ...appConfig,
      providers: [
        ...(appConfig.providers || []),
        { provide: KeycloakService, useValue: keycloakService },
        provideAnimations(),
      ],
    }).catch((err) => console.error(err));
  })
  .catch((err) => {
    console.error('Keycloak init error:', err);
  });
