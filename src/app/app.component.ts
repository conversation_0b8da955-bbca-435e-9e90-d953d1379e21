import { Component, OnDestroy, OnInit, inject } from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterModule,
} from '@angular/router';
import { filter, map, Subscription, switchMap } from 'rxjs';
import { CommonModule } from '@angular/common';
import { LayoutComponent } from './features/layout/layout.component';
import { KeycloakService } from './core/services/keycloak.service';
import { HeaderNavItems } from './core/constants/nav-items';
import { TitleService } from './core/services/title.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterModule, LayoutComponent],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'app-areadocs';
  private routerSubscription: Subscription | undefined;
  navItems = HeaderNavItems;
  showFooter = true;
  isLoggedIn = false;
  username = '';
  email = '';
  showSidebar = true;
  showHeader = true;
  profileImageUrl: string | null = null;

  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);
  private titleService = inject(TitleService);

  constructor(private keycloakService: KeycloakService) {}

  ngOnInit(): void {
    this.setPageTitle();
    // Check initial login status
    this.isLoggedIn = this.keycloakService.isLoggedIn();

    if (this.isLoggedIn) {
      this.username = this.keycloakService.getName() ?? '';
      this.email = this.keycloakService.getEmail() ?? '';
    }

    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const currentRoute = event.urlAfterRedirects;
        this.showFooter = ![
          '/login',
          '/register',
          '/home',
          '/landing',
        ].includes(currentRoute);
        // Hide sidebar on login and register pages
        this.showSidebar =
          this.isLoggedIn &&
          !['/login', '/register', '/landing'].includes(currentRoute);
        this.showHeader =
          this.isLoggedIn &&
          !['/login', '/register', '/landing'].includes(currentRoute);
      }
    });
  }

  login(): void {
    this.keycloakService.login();
  }

  logout(): void {
    this.keycloakService.logout();
  }

  filteredNavItems() {
    return this.navItems.filter(
      (item) => item.requiresAuth === this.isLoggedIn,
    );
  }

  ngOnDestroy() {
    this.routerSubscription?.unsubscribe();
  }

  navigateToRegister() {
    this.router.navigate(['/register']);
  }

  setPageTitle() {
    this.router?.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.activatedRoute),
        map((route) => {
          while (route.firstChild) {
            route = route.firstChild;
          }
          return route;
        }),
        filter((route) => route.outlet === 'primary'),
        switchMap((route) => route.data),
      )
      .subscribe((data) => {
        this.titleService.setTitle(data['title'] || '');
      });
  }
}
