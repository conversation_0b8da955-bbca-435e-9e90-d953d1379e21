import { Component, OnInit, inject } from '@angular/core';
import { KeycloakService } from '../../core/services/keycloak.service';

@Component({
  selector: 'app-login',
  standalone: true,
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  private keycloak = inject(KeycloakService);

  ngOnInit(): void {
    this.keycloak.login();
  }
}

