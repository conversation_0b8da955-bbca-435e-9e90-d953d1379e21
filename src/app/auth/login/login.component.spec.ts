// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { ReactiveFormsModule, FormsModule } from '@angular/forms';
// import { Router } from '@angular/router';
// import { LoginComponent } from './login.component';
// import { By } from '@angular/platform-browser';
// import { NzCardModule } from 'ng-zorro-antd/card';
// import { NzFormModule } from 'ng-zorro-antd/form';
// import { NzInputModule } from 'ng-zorro-antd/input';
// import { NzButtonModule } from 'ng-zorro-antd/button';
// import { NoopAnimationsModule } from '@angular/platform-browser/animations';
// import { HttpClientModule } from '@angular/common/http';

// describe('LoginComponent', () => {
//   let component: LoginComponent;
//   let fixture: ComponentFixture<LoginComponent>;

//   const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [
//         FormsModule,
//         ReactiveFormsModule,
//         LoginComponent,
//         NzCardModule,
//         NzFormModule,
//         NzInputModule,
//         NzButtonModule,
//         NoopAnimationsModule,
//         HttpClientModule,
//       ],
//       providers: [
//         { provide: Router, useValue: routerSpy }
//       ]
//     }).compileComponents();

//     fixture = TestBed.createComponent(LoginComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create the component', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should initialize with empty form controls', () => {
//     const email = component.loginForm.get('email')?.value;
//     const password = component.loginForm.get('password')?.value;
//     expect(email).toBe('');
//     expect(password).toBe('');
//   });

//   it('should have required validators for email and password', () => {
//     const email = component.loginForm.get('email');
//     const password = component.loginForm.get('password');

//     email?.setValue('');
//     password?.setValue('');

//     expect(email?.hasError('required')).toBeTrue();
//     expect(password?.hasError('required')).toBeTrue();
//   });

//   it('should validate proper and improper email formats', () => {
//     const email = component.loginForm.get('email');

//     email?.setValue('invalid-email');
//     expect(email?.hasError('email')).toBeTrue();

//     email?.setValue('<EMAIL>');
//     expect(email?.valid).toBeTrue();
//   });

//   it('should mark form as invalid when controls are empty', () => {
//     component.loginForm.get('email')?.setValue('');
//     component.loginForm.get('password')?.setValue('');
//     expect(component.loginForm.invalid).toBeTrue();
//   });

//   it('should mark form as valid with proper inputs', () => {
//     component.loginForm.get('email')?.setValue('<EMAIL>');
//     component.loginForm.get('password')?.setValue('securepass');
//     expect(component.loginForm.valid).toBeTrue();
//   });

//   it('should call navigateToRegister and route to /register', () => {
//     component.navigateToRegister();
//     expect(routerSpy.navigate).toHaveBeenCalledWith(['/register']);
//   });

//   it('should not proceed with submit if form is invalid', () => {
//     spyOn(console, 'log');
//     component.loginForm.get('email')?.setValue('');
//     component.loginForm.get('password')?.setValue('');
//     component.onSubmit();
//     expect(console.log).not.toHaveBeenCalled();
//   });

//   it('should log form value when form is valid and submitted', () => {
//     spyOn(console, 'log');
//     component.loginForm.get('email')?.setValue('<EMAIL>');
//     component.loginForm.get('password')?.setValue('password123');
//     component.onSubmit();
//     expect(console.log).toHaveBeenCalledWith('Login successful', {
//       email: '<EMAIL>',
//       password: 'password123'
//     });
//   });

//   it('should trigger onSubmit when form is submitted via template', () => {
//     const onSubmitSpy = spyOn(component, 'onSubmit').and.callThrough();
//     component.loginForm.get('email')?.setValue('<EMAIL>');
//     component.loginForm.get('password')?.setValue('password123');
//     fixture.detectChanges();

//     const form = fixture.debugElement.query(By.css('form'));
//     form.triggerEventHandler('ngSubmit', {});
//     fixture.detectChanges();

//     expect(onSubmitSpy).toHaveBeenCalled();
//   });

//   it('should disable login button when form is invalid', () => {
//     component.loginForm.get('email')?.setValue('');
//     component.loginForm.get('password')?.setValue('');
//     fixture.detectChanges();

//     const button = fixture.debugElement.query(By.css('button[nzType="primary"]')).nativeElement;
//     expect(button.disabled).toBeTrue();
//   });

//   it('should enable login button when form is valid', () => {
//     component.loginForm.get('email')?.setValue('<EMAIL>');
//     component.loginForm.get('password')?.setValue('password123');
//     fixture.detectChanges();

//     const button = fixture.debugElement.query(By.css('button[nzType="primary"]')).nativeElement;
//     expect(button.disabled).toBeFalse();
//   });
// });
