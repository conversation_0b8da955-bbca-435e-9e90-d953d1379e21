/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PageableObject } from './pageableObject';
import { SortObject } from './sortObject';
import { ProductCodeResponseDTO } from './productCodeResponseDTO';

export interface PageProductCodeResponseDTO {
  totalElements?: number;
  totalPages?: number;
  first?: boolean;
  last?: boolean;
  pageable?: PageableObject;
  size?: number;
  content?: Array<ProductCodeResponseDTO>;
  number?: number;
  sort?: SortObject;
  numberOfElements?: number;
  empty?: boolean;
}
