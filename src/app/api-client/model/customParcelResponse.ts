/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ParcelTitle } from './parcelTitle';
import { ParcelDetails } from './parcelDetails';
import { PropertyDTO } from './propertyDTO';

export interface CustomParcelResponse {
  spi?: string;
  parcelType?: CustomParcelResponse.ParcelTypeEnum;
  lotType?: CustomParcelResponse.LotTypeEnum;
  parcelStatus?: CustomParcelResponse.ParcelStatusEnum;
  parcelDetails?: ParcelDetails;
  titles?: Array<ParcelTitle>;
  properties?: Array<PropertyDTO>;
}
export namespace CustomParcelResponse {
  export const ParcelTypeEnum = {
    CommonProperty: 'COMMON_PROPERTY',
    Lot: 'LOT',
    Reserve: 'RESERVE',
    Road: 'ROAD',
    Unit: 'UNIT',
  } as const;
  export type ParcelTypeEnum =
    (typeof ParcelTypeEnum)[keyof typeof ParcelTypeEnum];
  export const LotTypeEnum = {
    AccessoryUnit: 'ACCESSORY_UNIT',
    Carpark: 'CARPARK',
    Restricted: 'RESTRICTED',
    Unrestricted: 'UNRESTRICTED',
  } as const;
  export type LotTypeEnum = (typeof LotTypeEnum)[keyof typeof LotTypeEnum];
  export const ParcelStatusEnum = {
    Active: 'ACTIVE',
    Proposed: 'PROPOSED',
  } as const;
  export type ParcelStatusEnum =
    (typeof ParcelStatusEnum)[keyof typeof ParcelStatusEnum];
}
