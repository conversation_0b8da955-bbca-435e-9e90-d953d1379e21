/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TransactionDTO } from './transactionDTO';
import { OrderItemDTO } from './orderItemDTO';

export interface OrderResponseDTO {
  id?: number;
  externalOrderId?: string;
  orderDate?: string;
  totalAmount?: number;
  totalTax?: number;
  status?: OrderResponseDTO.StatusEnum;
  paymentMethod?: string;
  items?: Array<OrderItemDTO>;
  transaction?: TransactionDTO;
}
export namespace OrderResponseDTO {
  export const StatusEnum = {
    Pending: 'PENDING',
    Paid: 'PAID',
    Processing: 'PROCESSING',
    Completed: 'COMPLETED',
    Cancelled: 'CANCELLED',
  } as const;
  export type StatusEnum = (typeof StatusEnum)[keyof typeof StatusEnum];
}
