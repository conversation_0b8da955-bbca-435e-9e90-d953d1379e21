/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { DocumentDTO } from './documentDTO';

export interface OrderItemDTO {
  id?: number;
  productCode?: string;
  productName?: string;
  documentType?: string;
  titleId?: string;
  price?: number;
  tax?: number;
  finalPrice?: number;
  status?: string;
  document?: DocumentDTO;
}
