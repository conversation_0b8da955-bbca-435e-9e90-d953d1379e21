/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressRequestDTO } from './addressRequestDTO';

export interface UserRequestDTO {
  email: string;
  password?: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  isActive?: boolean;
  userType: UserRequestDTO.UserTypeEnum;
  isBillingPrimary?: boolean;
  contactNumber?: string;
  bio?: string;
  roleId: number;
  companyId?: number;
  primaryAddress: AddressRequestDTO;
  billingAddress: AddressRequestDTO;
  removeProfilePicture?: boolean;
  profilePictureUrl?: string;
}
export namespace UserRequestDTO {
  export const UserTypeEnum = {
    Individual: 'INDIVIDUAL',
    Company: 'COMPANY',
  } as const;
  export type UserTypeEnum = (typeof UserTypeEnum)[keyof typeof UserTypeEnum];
}
