/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface UserRoleDTO {
  id?: number;
  readonly createdAt?: string;
  readonly modifiedAt?: string;
  readonly isActive?: boolean;
  readonly version?: number;
  readonly deleteAt?: string;
  userId?: number;
  userEmail?: string;
  userFirstName?: string;
  userLastName?: string;
  roleId?: number;
  roleName?: string;
  roleDisplayText?: string;
}
