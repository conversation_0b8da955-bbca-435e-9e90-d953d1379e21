/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { CouncilPropertyDetailsDTO } from './councilPropertyDetailsDTO';
import { TitleDTO } from './titleDTO';
import { AddressDTO } from './addressDTO';
import { LandParcelDTO } from './landParcelDTO';

export interface PropertyDTO {
  propertyPfi?: number;
  propertyStatus?: PropertyDTO.PropertyStatusEnum;
  isCouncilPropertyRegistered?: boolean;
  councilPropertyDetails?: CouncilPropertyDetailsDTO;
  isMultiAssess?: boolean;
  propertyCreatedDate?: string;
  primaryAddress?: AddressDTO;
  aliasAddresses?: Array<AddressDTO>;
  landParcels?: Array<LandParcelDTO>;
  titles?: Array<TitleDTO>;
}
export namespace PropertyDTO {
  export const PropertyStatusEnum = {
    Active: 'ACTIVE',
    Proposed: 'PROPOSED',
  } as const;
  export type PropertyStatusEnum =
    (typeof PropertyStatusEnum)[keyof typeof PropertyStatusEnum];
}
