/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface DocumentPriceResponseDTO {
  id?: number;
  readonly createdAt?: string;
  readonly modifiedAt?: string;
  readonly isActive?: boolean;
  readonly version?: number;
  readonly deleteAt?: string;
  documentTypeId?: number;
  productCodeId?: number;
  basePrice?: number;
  effectiveBasePrice?: number;
  effectiveDate?: string;
  expiryDate?: string;
  gst?: number;
}
