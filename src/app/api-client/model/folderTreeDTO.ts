/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface FolderTreeDTO {
  id?: number;
  readonly createdAt?: string;
  readonly modifiedAt?: string;
  readonly isActive?: boolean;
  readonly version?: number;
  readonly deleteAt?: string;
  name?: string;
  folderType?: FolderTreeDTO.FolderTypeEnum;
  childCount?: number;
  children?: Array<FolderTreeDTO>;
}
export namespace FolderTreeDTO {
  export const FolderTypeEnum = {
    Root: 'ROOT',
    Downloads: 'DOWNLOADS',
    Shared: 'SHARED',
    Private: 'PRIVATE',
  } as const;
  export type FolderTypeEnum =
    (typeof FolderTypeEnum)[keyof typeof FolderTypeEnum];
}
