/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface AddressResponseDTO {
  addressLine1?: string;
  addressLine2?: string;
  suburb?: string;
  stateId?: number;
  stateName?: string;
  stateAbbr?: string;
  zipCodeId?: number;
  zipCode?: string;
  addressType?: AddressResponseDTO.AddressTypeEnum;
}
export namespace AddressResponseDTO {
  export const AddressTypeEnum = {
    Primary: 'PRIMARY',
    Billing: 'BILLING',
  } as const;
  export type AddressTypeEnum =
    (typeof AddressTypeEnum)[keyof typeof AddressTypeEnum];
}
