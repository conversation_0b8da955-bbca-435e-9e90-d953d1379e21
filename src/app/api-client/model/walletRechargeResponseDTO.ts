/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface WalletRechargeResponseDTO {
  accountEntryId?: number;
  referenceNumber?: string;
  entryDate?: string;
  status?: WalletRechargeResponseDTO.StatusEnum;
  amount?: number;
  currency?: string;
  newBalance?: number;
  description?: string;
  transactionId?: number;
  transactionType?: WalletRechargeResponseDTO.TransactionTypeEnum;
  transactionDate?: string;
  transactionStatus?: WalletRechargeResponseDTO.TransactionStatusEnum;
  paymentTransactionId?: number;
  orderId?: string;
  paymentTxnId?: string;
  paymentStatus?: WalletRechargeResponseDTO.PaymentStatusEnum;
  paymentTimestamp?: string;
  paymentMethod?: string;
  paymentGateway?: string;
}
export namespace WalletRechargeResponseDTO {
  export const StatusEnum = {
    Pending: 'PENDING',
    Posted: 'POSTED',
    Voided: 'VOIDED',
  } as const;
  export type StatusEnum = (typeof StatusEnum)[keyof typeof StatusEnum];
  export const TransactionTypeEnum = {
    Recharge: 'RECHARGE',
    Purchase: 'PURCHASE',
    Refund: 'REFUND',
  } as const;
  export type TransactionTypeEnum =
    (typeof TransactionTypeEnum)[keyof typeof TransactionTypeEnum];
  export const TransactionStatusEnum = {
    Pending: 'PENDING',
    Completed: 'COMPLETED',
    Failed: 'FAILED',
    Refunded: 'REFUNDED',
  } as const;
  export type TransactionStatusEnum =
    (typeof TransactionStatusEnum)[keyof typeof TransactionStatusEnum];
  export const PaymentStatusEnum = {
    Pending: 'PENDING',
    Completed: 'COMPLETED',
    Failed: 'FAILED',
    Refunded: 'REFUNDED',
    Disputed: 'DISPUTED',
  } as const;
  export type PaymentStatusEnum =
    (typeof PaymentStatusEnum)[keyof typeof PaymentStatusEnum];
}
