/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface FolderRequestDTO {
  name: string;
  parentFolderId?: number;
  folderType?: FolderRequestDTO.FolderTypeEnum;
}
export namespace FolderRequestDTO {
  export const FolderTypeEnum = {
    Root: 'ROOT',
    Downloads: 'DOWNLOADS',
    Shared: 'SHARED',
    Private: 'PRIVATE',
  } as const;
  export type FolderTypeEnum =
    (typeof FolderTypeEnum)[keyof typeof FolderTypeEnum];
}
