/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface TransactionDTO {
  transactionId?: number;
  transactionType?: TransactionDTO.TransactionTypeEnum;
  transactionDate?: string;
  amount?: number;
  status?: TransactionDTO.StatusEnum;
  referenceId?: string;
  paymentMethod?: string;
}
export namespace TransactionDTO {
  export const TransactionTypeEnum = {
    Recharge: 'RECHARGE',
    Purchase: 'PURCHASE',
    Refund: 'REFUND',
  } as const;
  export type TransactionTypeEnum =
    (typeof TransactionTypeEnum)[keyof typeof TransactionTypeEnum];
  export const StatusEnum = {
    Pending: 'PENDING',
    Completed: 'COMPLETED',
    Failed: 'FAILED',
    Refunded: 'REFUNDED',
  } as const;
  export type StatusEnum = (typeof StatusEnum)[keyof typeof StatusEnum];
}
