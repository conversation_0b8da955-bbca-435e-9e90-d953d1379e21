/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressResponseDTO } from './addressResponseDTO';

export interface UserResponseDTO {
  id?: number;
  readonly createdAt?: string;
  readonly modifiedAt?: string;
  readonly isActive?: boolean;
  readonly version?: number;
  readonly deleteAt?: string;
  email?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  contactNumber?: string;
  userType?: string;
  lastLogin?: string;
  bio?: string;
  profilePictureUrl?: string;
  companyId?: number;
  roleId?: number;
  roleName?: string;
  roleDisplayText?: string;
  primaryAddress?: AddressResponseDTO;
  billingAddress?: AddressResponseDTO;
  keycloakId?: string;
}
