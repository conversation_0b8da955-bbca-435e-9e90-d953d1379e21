/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface TitlePropertyDTO {
  propertyPfi?: number;
  isMultiAssess?: boolean;
  propertyStatus?: TitlePropertyDTO.PropertyStatusEnum;
}
export namespace TitlePropertyDTO {
  export const PropertyStatusEnum = {
    Active: 'ACTIVE',
    Proposed: 'PROPOSED',
  } as const;
  export type PropertyStatusEnum =
    (typeof PropertyStatusEnum)[keyof typeof PropertyStatusEnum];
}
