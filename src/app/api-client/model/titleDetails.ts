/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface TitleDetails {
  titleId?: string;
  titleStatus?: TitleDetails.TitleStatusEnum;
  titleType?: TitleDetails.TitleTypeEnum;
  landDescriptionText?: string;
  municipality?: string;
}
export namespace TitleDetails {
  export const TitleStatusEnum = {
    Active: 'ACTIVE',
    Cancelled: 'CANCELLED',
    PartiallyCancelled: 'PARTIALLY_CANCELLED',
  } as const;
  export type TitleStatusEnum =
    (typeof TitleStatusEnum)[keyof typeof TitleStatusEnum];
  export const TitleTypeEnum = {
    AlpineLease: 'ALPINE_LEASE',
    CityLinkLease: 'CITY_LINK_LEASE',
    CrownGrant: 'CROWN_GRANT',
    CrownLand: 'CROWN_LAND',
    CrownLease: 'CROWN_LEASE',
    Freehold: 'FREEHOLD',
    Identified: 'IDENTIFIED',
    MineralExcludes: 'MINERAL_EXCLUDES',
    MineralIncludes: 'MINERAL_INCLUDES',
    TreasurersReceipt: 'TREASURERS_RECEIPT',
  } as const;
  export type TitleTypeEnum =
    (typeof TitleTypeEnum)[keyof typeof TitleTypeEnum];
}
