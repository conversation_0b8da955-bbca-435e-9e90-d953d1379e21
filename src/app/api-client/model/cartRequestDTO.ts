/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface CartRequestDTO {
  folderId?: number;
  productCode?: string;
  propertyPfi?: string;
  volumeFolio?: string;
  isWarningAcknowledged?: boolean;
  landUseApplicationReason?: CartRequestDTO.LandUseApplicationReasonEnum;
}
export namespace CartRequestDTO {
  export const LandUseApplicationReasonEnum = {
    Vendor: 'VENDOR',
    Purchaser: 'PURCHASER',
    Mortgagee: 'MORTGAGEE',
    Other: 'OTHER',
  } as const;
  export type LandUseApplicationReasonEnum =
    (typeof LandUseApplicationReasonEnum)[keyof typeof LandUseApplicationReasonEnum];
}
