/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressRequestDTO } from './addressRequestDTO';

export interface CompanyRequestDTO {
  name: string;
  industry?: string;
  description?: string;
  website?: string;
  employeeCount?: number;
  isBillingPrimary?: boolean;
  billingEmail: string;
  accountsContactNumber?: string;
  accountsContactName?: string;
  primaryAddress: AddressRequestDTO;
  billingAddress: AddressRequestDTO;
  abn?: string;
  acn?: string;
}
