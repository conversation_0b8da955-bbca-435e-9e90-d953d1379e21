/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PageableObject } from './pageableObject';
import { SortObject } from './sortObject';
import { DocumentDTO } from './documentDTO';

export interface PageDocumentDTO {
  totalElements?: number;
  totalPages?: number;
  first?: boolean;
  last?: boolean;
  pageable?: PageableObject;
  size?: number;
  content?: Array<DocumentDTO>;
  number?: number;
  sort?: SortObject;
  numberOfElements?: number;
  empty?: boolean;
}
