/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface AccountDTO {
  id?: number;
  accountType?: AccountDTO.AccountTypeEnum;
  userId?: number;
  companyId?: number;
  balance?: number;
  currency?: string;
  isActive?: boolean;
}
export namespace AccountDTO {
  export const AccountTypeEnum = {
    Individual: 'INDIVIDUAL',
    Company: 'COMPANY',
    Platform: 'PLATFORM',
  } as const;
  export type AccountTypeEnum =
    (typeof AccountTypeEnum)[keyof typeof AccountTypeEnum];
}
