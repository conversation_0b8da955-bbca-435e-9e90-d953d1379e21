/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface DocumentDTO {
  id?: number;
  readonly createdAt?: string;
  readonly modifiedAt?: string;
  readonly isActive?: boolean;
  readonly version?: number;
  readonly deleteAt?: string;
  folderId?: number;
  folderName?: string;
  title?: string;
  description?: string;
  categoryId?: number;
  categoryName?: string;
  filePath?: string;
  metadata?: { [key: string]: object };
  lastAccessed?: string;
  accessedCount?: number;
  expiryDate?: string;
}
