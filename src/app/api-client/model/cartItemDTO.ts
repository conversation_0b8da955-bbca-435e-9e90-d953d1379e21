/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export interface CartItemDTO {
  id?: number;
  productCode?: string;
  productName?: string;
  folderId?: number;
  propertyPfi?: string;
  volumeFolio?: string;
  isWarningAcknowledged?: boolean;
  price?: number;
  tax?: number;
  finalPrice?: number;
  status?: CartItemDTO.StatusEnum;
}
export namespace CartItemDTO {
  export const StatusEnum = {
    Cart: 'CART',
    Pending: 'PENDING',
    InTransit: 'IN_TRANSIT',
    Delivered: 'DELIVERED',
    Complete: 'COMPLETE',
    Processing: 'PROCESSING',
    ExternalProcessing: 'EXTERNAL_PROCESSING',
    Failed: 'FAILED',
    Cancelled: 'CANCELLED',
  } as const;
  export type StatusEnum = (typeof StatusEnum)[keyof typeof StatusEnum];
}
