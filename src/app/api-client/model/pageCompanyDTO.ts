/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PageableObject } from './pageableObject';
import { SortObject } from './sortObject';
import { CompanyDTO } from './companyDTO';

export interface PageCompanyDTO {
  totalElements?: number;
  totalPages?: number;
  first?: boolean;
  last?: boolean;
  pageable?: PageableObject;
  size?: number;
  content?: Array<CompanyDTO>;
  number?: number;
  sort?: SortObject;
  numberOfElements?: number;
  empty?: boolean;
}
