/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { DocumentPriceResponseDTO } from './documentPriceResponseDTO';
import { PageableObject } from './pageableObject';
import { SortObject } from './sortObject';

export interface PageDocumentPriceResponseDTO {
  totalElements?: number;
  totalPages?: number;
  first?: boolean;
  last?: boolean;
  pageable?: PageableObject;
  size?: number;
  content?: Array<DocumentPriceResponseDTO>;
  number?: number;
  sort?: SortObject;
  numberOfElements?: number;
  empty?: boolean;
}
