/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import {
  HttpClient,
  HttpHeaders,
  HttpParams,
  HttpResponse,
  HttpEvent,
  HttpParameterCodec,
  HttpContext,
} from '@angular/common/http';
import { CustomHttpParameterCodec } from '../encoder';
import { Observable } from 'rxjs';

// @ts-ignore
import { PresignedUrlResponse } from '../model/presignedUrlResponse';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS } from '../variables';
import { Configuration } from '../configuration';
import { BaseService } from '../api.base.service';

@Injectable({
  providedIn: 'root',
})
export class S3ControllerService extends BaseService {
  constructor(
    protected httpClient: HttpClient,
    @Optional() @Inject(BASE_PATH) basePath: string | string[],
    @Optional() configuration?: Configuration,
  ) {
    super(basePath, configuration);
  }

  /**
   * @param userEmail
   * @param fileName
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getPresignedUrl(
    userEmail: string,
    fileName: string,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<PresignedUrlResponse>;
  public getPresignedUrl(
    userEmail: string,
    fileName: string,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<PresignedUrlResponse>>;
  public getPresignedUrl(
    userEmail: string,
    fileName: string,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<PresignedUrlResponse>>;
  public getPresignedUrl(
    userEmail: string,
    fileName: string,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    if (userEmail === null || userEmail === undefined) {
      throw new Error(
        'Required parameter userEmail was null or undefined when calling getPresignedUrl.',
      );
    }
    if (fileName === null || fileName === undefined) {
      throw new Error(
        'Required parameter fileName was null or undefined when calling getPresignedUrl.',
      );
    }

    let localVarQueryParameters = new HttpParams({ encoder: this.encoder });
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>userEmail,
      'userEmail',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>fileName,
      'fileName',
    );

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/s3/presigned-url`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<PresignedUrlResponse>(
      'get',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        params: localVarQueryParameters,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }
}
