/**
 * Areadocs Documentation
 *
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional } from '@angular/core';
import {
  HttpClient,
  HttpHeaders,
  HttpParams,
  HttpResponse,
  HttpEvent,
  HttpParameterCodec,
  HttpContext,
} from '@angular/common/http';
import { CustomHttpParameterCodec } from '../encoder';
import { Observable } from 'rxjs';

// @ts-ignore
import { ApiResponseDocumentDTO } from '../model/apiResponseDocumentDTO';
// @ts-ignore
import { ApiResponsePageDocumentDTO } from '../model/apiResponsePageDocumentDTO';
// @ts-ignore
import { DocumentUpdateDTO } from '../model/documentUpdateDTO';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS } from '../variables';
import { Configuration } from '../configuration';
import { BaseService } from '../api.base.service';

@Injectable({
  providedIn: 'root',
})
export class DocumentControllerService extends BaseService {
  constructor(
    protected httpClient: HttpClient,
    @Optional() @Inject(BASE_PATH) basePath: string | string[],
    @Optional() configuration?: Configuration,
  ) {
    super(basePath, configuration);
  }

  /**
   * Delete document
   * Deletes a document by ID
   * @param id
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public deleteDocument(
    id: number,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<string>;
  public deleteDocument(
    id: number,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<string>>;
  public deleteDocument(
    id: number,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<string>>;
  public deleteDocument(
    id: number,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error(
        'Required parameter id was null or undefined when calling deleteDocument.',
      );
    }

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/documents/${this.configuration.encodeParam({ name: 'id', value: id, in: 'path', style: 'simple', explode: false, dataType: 'number', dataFormat: 'int64' })}`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<string>(
      'delete',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }

  /**
   * Get documents
   * Retrieves documents based on provided filters with pagination
   * @param documentId
   * @param title
   * @param description
   * @param externalId
   * @param folderId
   * @param sourceId
   * @param categoryId
   * @param documentType
   * @param minBasePrice
   * @param maxBasePrice
   * @param minFinalPrice
   * @param maxFinalPrice
   * @param minAccessCount
   * @param maxAccessCount
   * @param page
   * @param size
   * @param sort
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public getDocuments(
    documentId?: number,
    title?: string,
    description?: string,
    externalId?: number,
    folderId?: number,
    sourceId?: number,
    categoryId?: number,
    documentType?: string,
    minBasePrice?: number,
    maxBasePrice?: number,
    minFinalPrice?: number,
    maxFinalPrice?: number,
    minAccessCount?: number,
    maxAccessCount?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<ApiResponsePageDocumentDTO>;
  public getDocuments(
    documentId?: number,
    title?: string,
    description?: string,
    externalId?: number,
    folderId?: number,
    sourceId?: number,
    categoryId?: number,
    documentType?: string,
    minBasePrice?: number,
    maxBasePrice?: number,
    minFinalPrice?: number,
    maxFinalPrice?: number,
    minAccessCount?: number,
    maxAccessCount?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<ApiResponsePageDocumentDTO>>;
  public getDocuments(
    documentId?: number,
    title?: string,
    description?: string,
    externalId?: number,
    folderId?: number,
    sourceId?: number,
    categoryId?: number,
    documentType?: string,
    minBasePrice?: number,
    maxBasePrice?: number,
    minFinalPrice?: number,
    maxFinalPrice?: number,
    minAccessCount?: number,
    maxAccessCount?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<ApiResponsePageDocumentDTO>>;
  public getDocuments(
    documentId?: number,
    title?: string,
    description?: string,
    externalId?: number,
    folderId?: number,
    sourceId?: number,
    categoryId?: number,
    documentType?: string,
    minBasePrice?: number,
    maxBasePrice?: number,
    minFinalPrice?: number,
    maxFinalPrice?: number,
    minAccessCount?: number,
    maxAccessCount?: number,
    page?: number,
    size?: number,
    sort?: string,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    let localVarQueryParameters = new HttpParams({ encoder: this.encoder });
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>documentId,
      'documentId',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>title,
      'title',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>description,
      'description',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>externalId,
      'externalId',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>folderId,
      'folderId',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>sourceId,
      'sourceId',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>categoryId,
      'categoryId',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>documentType,
      'documentType',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>minBasePrice,
      'minBasePrice',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>maxBasePrice,
      'maxBasePrice',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>minFinalPrice,
      'minFinalPrice',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>maxFinalPrice,
      'maxFinalPrice',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>minAccessCount,
      'minAccessCount',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>maxAccessCount,
      'maxAccessCount',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>page,
      'page',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>size,
      'size',
    );
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>sort,
      'sort',
    );

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/documents`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<ApiResponsePageDocumentDTO>(
      'get',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        params: localVarQueryParameters,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }

  /**
   * Move document
   * Moves a document to a different folder.
   * @param documentId
   * @param newFolderId
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public moveDocument(
    documentId: number,
    newFolderId: number,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<ApiResponseDocumentDTO>;
  public moveDocument(
    documentId: number,
    newFolderId: number,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<ApiResponseDocumentDTO>>;
  public moveDocument(
    documentId: number,
    newFolderId: number,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<ApiResponseDocumentDTO>>;
  public moveDocument(
    documentId: number,
    newFolderId: number,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    if (documentId === null || documentId === undefined) {
      throw new Error(
        'Required parameter documentId was null or undefined when calling moveDocument.',
      );
    }
    if (newFolderId === null || newFolderId === undefined) {
      throw new Error(
        'Required parameter newFolderId was null or undefined when calling moveDocument.',
      );
    }

    let localVarQueryParameters = new HttpParams({ encoder: this.encoder });
    localVarQueryParameters = this.addToHttpParams(
      localVarQueryParameters,
      <any>newFolderId,
      'newFolderId',
    );

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/documents/${this.configuration.encodeParam({ name: 'documentId', value: documentId, in: 'path', style: 'simple', explode: false, dataType: 'number', dataFormat: 'int64' })}/move`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<ApiResponseDocumentDTO>(
      'put',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        params: localVarQueryParameters,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }

  /**
   * Update document
   * Updates a document\&#39;s title, description, or folder.
   * @param id
   * @param documentUpdateDTO
   * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
   * @param reportProgress flag to report request and response progress.
   */
  public updateDocument(
    id: number,
    documentUpdateDTO: DocumentUpdateDTO,
    observe?: 'body',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<ApiResponseDocumentDTO>;
  public updateDocument(
    id: number,
    documentUpdateDTO: DocumentUpdateDTO,
    observe?: 'response',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpResponse<ApiResponseDocumentDTO>>;
  public updateDocument(
    id: number,
    documentUpdateDTO: DocumentUpdateDTO,
    observe?: 'events',
    reportProgress?: boolean,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<HttpEvent<ApiResponseDocumentDTO>>;
  public updateDocument(
    id: number,
    documentUpdateDTO: DocumentUpdateDTO,
    observe: any = 'body',
    reportProgress: boolean = false,
    options?: {
      httpHeaderAccept?: 'application/json';
      context?: HttpContext;
      transferCache?: boolean;
    },
  ): Observable<any> {
    if (id === null || id === undefined) {
      throw new Error(
        'Required parameter id was null or undefined when calling updateDocument.',
      );
    }
    if (documentUpdateDTO === null || documentUpdateDTO === undefined) {
      throw new Error(
        'Required parameter documentUpdateDTO was null or undefined when calling updateDocument.',
      );
    }

    let localVarHeaders = this.defaultHeaders;

    // authentication (bearer-jwt) required
    localVarHeaders = this.configuration.addCredentialToHeaders(
      'bearer-jwt',
      'Authorization',
      localVarHeaders,
      'Bearer ',
    );

    const localVarHttpHeaderAcceptSelected: string | undefined =
      options?.httpHeaderAccept ??
      this.configuration.selectHeaderAccept(['application/json']);
    if (localVarHttpHeaderAcceptSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Accept',
        localVarHttpHeaderAcceptSelected,
      );
    }

    const localVarHttpContext: HttpContext =
      options?.context ?? new HttpContext();

    const localVarTransferCache: boolean = options?.transferCache ?? true;

    // to determine the Content-Type header
    const consumes: string[] = ['application/json'];
    const httpContentTypeSelected: string | undefined =
      this.configuration.selectHeaderContentType(consumes);
    if (httpContentTypeSelected !== undefined) {
      localVarHeaders = localVarHeaders.set(
        'Content-Type',
        httpContentTypeSelected,
      );
    }

    let responseType_: 'text' | 'json' | 'blob' = 'json';
    if (localVarHttpHeaderAcceptSelected) {
      if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
        responseType_ = 'text';
      } else if (
        this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)
      ) {
        responseType_ = 'json';
      } else {
        responseType_ = 'blob';
      }
    }

    let localVarPath = `/documents/${this.configuration.encodeParam({ name: 'id', value: id, in: 'path', style: 'simple', explode: false, dataType: 'number', dataFormat: 'int64' })}`;
    const { basePath, withCredentials } = this.configuration;
    return this.httpClient.request<ApiResponseDocumentDTO>(
      'put',
      `${basePath}${localVarPath}`,
      {
        context: localVarHttpContext,
        body: documentUpdateDTO,
        responseType: <any>responseType_,
        ...(withCredentials ? { withCredentials } : {}),
        headers: localVarHeaders,
        observe: observe,
        transferCache: localVarTransferCache,
        reportProgress: reportProgress,
      },
    );
  }
}
