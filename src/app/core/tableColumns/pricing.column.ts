import { cloneDeep } from 'lodash';
import {
  NUM_SORT_FN,
  STRING_CASE_INSENSITIVE_SORT_FN,
} from '../constants/functions';
import {
  IPricingFields,
  IPricingViewDetails,
} from '../interface/pricing-fields';
import { ITableColumn } from '../interface/table';

export const PRICING_MANAGEMENT_TABLE_COLUMNS: ITableColumn<IPricingFields>[] =
  [
    {
      header: 'Document Name',
      field: 'documentName',
      fieldType: 'text',
      width: '15rem',
      sortFn: (a, b) =>
        STRING_CASE_INSENSITIVE_SORT_FN(a.documentName, b.documentName),
    },
    {
      header: 'Description',
      field: 'description',
      fieldType: 'text',
      width: '15rem',
    },
    {
      header: 'Cost',
      field: 'cost',
      fieldType: 'text',
      width: '12rem',
      sortFn: (a, b) => NUM_SORT_FN(a.cost, b.cost),
    },
    {
      header: 'Product Price (Ex GST)',
      field: 'productPrice',
      fieldType: 'inputField',
      isEditable: true,
      width: '12rem',
      sortFn: (a, b) => NUM_SORT_FN(a.productPrice, b.productPrice),
      placeholder: 'Enter value...',
    },
    {
      header: 'GST',
      field: 'gst',
      fieldType: 'currency',
      width: '12rem',
      sortFn: (a, b) => NUM_SORT_FN(a.gst, b.gst),
    },
    {
      header: 'Product Price (In GST)',
      field: 'productPriceIncGst',
      fieldType: 'currency',
      width: '12rem',
      sortFn: (a, b) => NUM_SORT_FN(a.productPriceIncGst, b.productPriceIncGst),
    },
  ];

const VIEW_BUTTON: ITableColumn<IPricingFields>[] = [
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '6rem',
    actionFieldColumns: [
      {
        field: 'view',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'eye',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'View Details',
      },
    ],
  },
];

const EDIT_DELETE_BUTTONS: ITableColumn<IPricingFields>[] = [
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '6rem',
    actionFieldColumns: [
      {
        field: 'edit',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'edit',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Edit Document',
      },
      {
        field: 'delete',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'delete',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Delete Document',
      },
    ],
  },
];

export const VIEW_PRICING_DETAILS_COLUMNS: ITableColumn<IPricingViewDetails>[] =
  [
    {
      header: 'Doc Price',
      field: 'documentPrice',
      fieldType: 'currency',
      width: '5rem',
    },
    {
      header: 'Date & Time',
      field: 'dateTime',
      fieldType: 'text',
      width: '5rem',
    },
    {
      header: 'Updated By',
      field: 'updatedBy',
      fieldType: 'textWithCaption',
      width: '10rem',
      getText: (rowData: IPricingViewDetails) => rowData.updatedBy || 'N/A',
      getCaption: (rowData: IPricingViewDetails) =>
        rowData.billingEmail || 'N/A',
    },
  ];

export const PRICING_MANAGEMENT_COMPANY_LEVEL_TABLE_COLUMNS: ITableColumn<IPricingFields>[] =
  cloneDeep([...PRICING_MANAGEMENT_TABLE_COLUMNS, ...VIEW_BUTTON]);

export const PRICING_MANAGEMENT_GLOBAL_LEVEL_TABLE_COLUMNS: ITableColumn<IPricingFields>[] =
  cloneDeep([...PRICING_MANAGEMENT_TABLE_COLUMNS, ...EDIT_DELETE_BUTTONS]);

export const DUMMY_PRICING_DATA: IPricingFields[] = [
  {
    documentName: 'Land Ownership Report',
    productCode: 'LO006',
    description: 'Detailed report on land ownership',
    cost: 55.0,
    productPrice: undefined,
    gst: 6.6,
    productPriceIncGst: 72.6,
  },
  {
    documentName: 'Zoning Certificate',
    productCode: 'ZC007',
    description: 'Certificate showing zoning of property',
    cost: 65.0,
    productPrice: 78.0,
    gst: 7.8,
    productPriceIncGst: 85.8,
  },
  {
    documentName: 'Survey Plan',
    productCode: 'SP008',
    description: 'Property boundaries and measurements',
    cost: 90.0,
    productPrice: undefined,
    gst: 10.8,
    productPriceIncGst: 118.8,
  },
  {
    documentName: 'Flood Zone Report',
    productCode: 'FZ009',
    description: 'Analysis of flood risks on the land',
    cost: 40.0,
    productPrice: 48.0,
    gst: 4.8,
    productPriceIncGst: 52.8,
  },
  {
    documentName: 'Heritage Report',
    productCode: 'HR010',
    description: 'Check for heritage listings on the property',
    cost: 75.0,
    productPrice: undefined,
    gst: 9.0,
    productPriceIncGst: 99.0,
  },
  {
    documentName: 'Planning Certificate',
    productCode: 'PC011',
    description: 'Planning regulations applicable to property',
    cost: 85.0,
    productPrice: 102.0,
    gst: 10.2,
    productPriceIncGst: 112.2,
  },
  {
    documentName: 'Certificate of Title',
    productCode: 'CT012',
    description: 'Official record of land ownership',
    cost: 48.0,
    productPrice: 57.0,
    gst: 5.7,
    productPriceIncGst: 62.7,
  },
  {
    documentName: 'Crown Land Status',
    productCode: 'CLS013',
    description: 'Status check on crown land ownership',
    cost: 60.0,
    productPrice: 72.0,
    gst: 7.2,
    productPriceIncGst: 79.2,
  },
  {
    documentName: 'Valuation Report',
    productCode: 'VR014',
    description: 'Estimated market value of the property',
    cost: 120.0,
    productPrice: 144.0,
    gst: 14.4,
    productPriceIncGst: 158.4,
  },
  {
    documentName: 'Encumbrance Report',
    productCode: 'ER015',
    description: 'Legal claims or restrictions on the land',
    cost: 70.0,
    productPrice: 84.0,
    gst: 8.4,
    productPriceIncGst: 92.4,
  },
];

export const DUMMY_PRICING_VIEW_DATA: IPricingViewDetails[] = [
  {
    documentPrice: 100.0,
    dateTime: '2023-05-30',
    updatedBy: 'John Doe',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 120.0,
    dateTime: '2023-06-01',
    updatedBy: 'Jane Smith',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 150.0,
    dateTime: '2023-06-02',
    updatedBy: 'Bob Johnson',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 180.0,
    dateTime: '2023-06-03',
    updatedBy: 'Alice Brown',
    billingEmail: '<EMAIL>',
  },
  {
    documentPrice: 200.0,
    dateTime: '2023-06-04',
    updatedBy: 'Tom Wilson',
    billingEmail: '<EMAIL>',
  },
];
