import {} from '../constants/functions';

import { ITableColumn } from '../interface/table';
import { ITransaction } from '../interface/transaction.interface';

export const TRANSACTION_TABLE_COLUMNS: ITableColumn<ITransaction>[] = [
  {
    header: 'Purchased Product',
    field: 'productName',
    fieldType: 'text',
    width: '25rem',

    altText: 'N/A',
    conditionForAltText: (rowData) => !rowData.productName,
  },

  {
    header: 'Issue Date',
    field: 'transactionDate',
    fieldType: 'dateField',
    width: '15rem',
    altText: 'N/A',
    conditionForAltText: (rowData) => !rowData.price,
  },
  {
    header: 'Expiry Date',
    field: 'expiryDate',
    fieldType: 'dateField',
    width: '15rem',
    altText: 'N/A',
    conditionForAltText: (rowData) => !rowData.expiryDate,
  },
  {
    header: 'Product Price',
    field: 'finalPrice',
    fieldType: 'text',
    width: '15rem',
  },
  {
    header: 'Status',
    field: 'status',
    fieldType: 'text',
    width: '15rem',
  },
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '7rem',
    actionFieldColumns: [
      {
        field: 'view',
        header: '',
        fieldType: 'icon',
        iconClasses: ['fa', 'fa-eye', 'blue-info-icon'],
        btnLabelFn: () => 'View',
      },
      {
        field: 'Download',
        header: '',
        fieldType: 'icon',
        iconClasses: ['fa', 'fa-download', 'blue-info-icon'],
        btnLabelFn: () => 'Download',
      },
    ],
  },
];
