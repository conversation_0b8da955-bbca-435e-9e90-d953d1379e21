// import { TestBed } from '@angular/core/testing';
// import {
//   Router,
//   ActivatedRouteSnapshot,
//   RouterStateSnapshot,
// } from '@angular/router';
// import { provideRouter } from '@angular/router';
// import { AuthService } from '../services/auth.service';
// import { authGuard } from './auth.guard';
// import { runInInjectionContext } from '@angular/core';

// describe('AuthGuard', () => {
//   let authService: jasmine.SpyObj<AuthService>;
//   let router: Router;
//   const route = {} as ActivatedRouteSnapshot;
//   const state = {} as RouterStateSnapshot;

//   beforeEach(() => {
//     authService = jasmine.createSpyObj('AuthService', ['isAuthenticated']);

//     TestBed.configureTestingModule({
//       providers: [
//         { provide: AuthService, useValue: authService },
//         provideRouter([]),
//       ],
//     });

//     router = TestBed.inject(Router);
//   });

//   it('should allow access when authenticated', () => {
//     authService.isAuthenticated.and.returnValue(true);

//     runInInjectionContext(TestBed, () => {
//       expect(authGuard(route, state)).toBeTrue();
//     });
//   });

//   it('should redirect to login when not authenticated', () => {
//     authService.isAuthenticated.and.returnValue(false);

//     runInInjectionContext(TestBed, () => {
//       expect(authGuard(route, state)).toEqual(router.createUrlTree(['/login']));
//     });
//   });
// });
