import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { KeycloakService } from '../../core/services/keycloak.service';
import { UserService } from '../services/user.service';
import { EnumUserRole } from '../enumerations/user-roles';

export const AUTH_GUARD: CanActivateFn = () => {
  const keycloakService = inject(KeycloakService);

  if (keycloakService.isLoggedIn()) {
    return true;
  } else {
    keycloakService.login();
    return false;
  }
};

export const roleGuard = (allowedRoles: EnumUserRole[]): CanActivateFn => {
  return async () => {
    const keycloakService = inject(KeycloakService);
    const userService = inject(UserService);
    const router = inject(Router);

    if (!keycloakService.isLoggedIn()) {
      keycloakService.login();
      return false;
    }
    const userRoleId = (await userService.getUserRole())?.roleId ?? 0;

    if (allowedRoles.includes(userRoleId)) {
      return true;
    }
    const fallback =
      userService.getPrimaryNativeRouteBasedOnUserRole(userRoleId);
    router.navigate([fallback]);
    return false;
  };
};

/**
 *
 *
 * ╔════════════════════════════════════════════════════════════════════════╗
 * ║                          🔐 roleGuard Usage                            ║
 * ╟────────────────────────────────────────────────────────────────────────╢
 * ║ Use `roleGuard` to protect routes based on user roles defined in       ║
 * ║ `EnumUserRole`. Pass an array of allowed roles. If the current user    ║
 * ║ does not have one of the allowed roles, they will be redirected.       ║
 * ║                                                                        ║
 * ║ ✅ Example 1: Platform Admin Access Only                               ║
 * ║                                                                        ║
 * ║ {                                                                      ║
 * ║   path: ROUTES.sidebar.dashboard,                                      ║
 * ║   component: DashboardComponent,                                       ║
 * ║   canActivate: [                                                       ║
 * ║     roleGuard([EnumUserRole.PLATFORM_ADMIN])                           ║
 * ║   ]                                                                    ║
 * ║ }                                                                      ║
 * ║                                                                        ║
 * ║ ✅ Example 2: Multiple Roles Access                                    ║
 * ║                                                                        ║
 * ║ {                                                                      ║
 * ║   path: ROUTES.sidebar.homepage,                                       ║
 * ║   component: HomepageComponent,                                        ║
 * ║   canActivate: [                                                       ║
 * ║     roleGuard([                                                        ║
 * ║       EnumUserRole.COMPANY_ADMIN,                                      ║
 * ║       EnumUserRole.COMPANY_MEMBER,                                     ║
 * ║       EnumUserRole.INDEPENDEDNT_AGENT                                  ║
 * ║     ])                                                                 ║
 * ║   ]                                                                    ║
 * ║ }                                                                      ║
 * ║                                                                        ║
 * ╚════════════════════════════════════════════════════════════════════════╝
 *
 *
 */
