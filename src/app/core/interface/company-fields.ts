import { AddressResponseDTO, CompanyDTO } from '../../api-client';
import { IUserFormData } from './user-fields';

export interface ICompanyFields extends CompanyDTO, AddressResponseDTO {
  id?: number;
  name?: string;
  accountsContactName?: string;
  billingEmail?: string;
  primaryAddress?: AddressResponseDTO;
  primaryAddressFormatted?: string;
  accountHolder?: string | null;
  totalUsers?: number;
  totalDocumentsOrdered?: number;
  totalDocumentPrice?: number;
  isActive?: boolean;
  abn?: string;
  acn?: string;
  accountsContactNumber?: string;
  isBillingPrimary?: boolean;
}

export interface AddressFormValue {
  addressLine1: string;
  addressLine2?: string;
  suburb?: string;
  state: number;
  postalCode: number;
}

export interface ICompanyFormData extends CompanyDTO {
  addressLine1: string;
  addressLine2?: string;
  suburb?: string;
  state: number | string;
  postalCode: number | string;
}

export interface IBillingFormData extends ICompanyFormData {
  sameAsCompanyDetails: boolean;
}

export interface OnboardingState {
  companyDetails: ICompanyFormData | null;
  billingDetails: IBillingFormData | null;
  userDetails: IUserFormData | null;
  currentStep: number;
}
