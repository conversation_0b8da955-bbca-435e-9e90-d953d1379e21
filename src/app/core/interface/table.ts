import {
  NzTable<PERSON><PERSON>er<PERSON>n,
  NzT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NzTableSortFn,
  NzTableSortOrder,
} from 'ng-zorro-antd/table';
import { TFieldType } from '../types/field.type';
import {
  TDisableFn,
  TGetBtnLabel,
  TGetClassNameFn,
  TGetCondition,
  TGetLabel,
  TGetTextFn,
  TGetToolTipTextFn,
  TNzToolTipPlacementTypes,
} from '../types/table.type';
import { IDropdownOption } from './common';
import { EnumSortType } from '../enumerations/sort-type';

export interface ITableDataClickOutput<T> {
  rowData: T;
  actionField: string;
  rowIndex: number;
  rowNumber?: number;
  link?: string;
}

export interface IRowClickOutput<T> {
  rowData: T;
  rowIndex: number;
  rowNumber: number;
}

export interface IFieldDataChangeOutput<T> {
  rowData: T;
  column?: ITableColumn<T>;
  event?: Event;
  value?: string | number | boolean | null;
}

export interface SortValues {
  SortOrder: EnumSortType | null;
  SortParam: string | null;
}

export interface ITableDataChangeOutput<T> {
  updatedData: T[];
  changeType?: 'sort' | 'filter' | 'drag&drop';
  changeValue?: SortValues | string | keyof T;
  column?: ITableColumn<T>;
}

export interface ITableColumn<T> {
  header: string; // Header can have children (subheaders)
  field: string; // Field name in the data
  fieldType: TFieldType;
  sortOrder?: NzTableSortOrder | null;
  sortFn?: NzTableSortFn<T> | null;
  listOfFilter?: NzTableFilterList;
  filterFn?: NzTableFilterFn<T> | null;
  width?: string | null; // Optional width for the column
  isFrozenLeft?: boolean; // Optional: Freeze column to the left
  isFrozenRight?: boolean; // Optional: Freeze column to the right
  dropDownVisible?: boolean;
  children?: ITableColumn<T>[]; // Subheaders (nested columns)'
  colspan?: number; // Colspan for the header
  rowspan?: number; // Rowspan for the header
  items?: IDropdownOption<T>[];
  bindName?: string;
  bindValue?: string;
  btnLabelText?: string;
  showTooltip?: boolean;
  tooltipText?: string;
  secondaryBtn?: boolean;
  altText?: string;
  tooltipPlacement?: TNzToolTipPlacementTypes;
  isDisabled?: 0 | 1 | boolean;
  isEditable?: 0 | 1 | boolean;
  iconClass?: string;
  iconClasses?: string[];
  buttonIconClass?: string;
  actionFieldColumns?: ITableColumn<T>[];
  iconTooltipTitle?: string;
  btnLabelFn?: TGetBtnLabel<T>;
  disableFn?: TDisableFn<T>;
  secondaryBtnFn?: TGetCondition<T>;
  getClassName?: TGetClassNameFn<T>;
  getAltText?: TGetTextFn<T>;
  getToolTipTextFn?: TGetToolTipTextFn<T>;
  conditionForShowActionField?: TGetCondition<T>;
  conditionForShowIcon?: TGetCondition<T>;
  conditionForAltText?: TGetCondition<T>;
  getIconToolTipTextFn?: TGetToolTipTextFn<T>;
  getBtnIconClassFn?: TGetClassNameFn<T>;
  getCaption?: TGetLabel<T>;
  getText?: TGetLabel<T>;
  svgIconPathFn?: TGetTextFn<T>;
  showBadgeOverIconFn?: TGetCondition<T>;
  showBadgeOverIcon?: boolean;
  iconType?: 'nzIcon' | 'fa-icon' | 'svgIcon';
  badgeIconType?: 'nzIcon' | 'fa-icon' | 'svgIcon' | 'number';
  svgIconPath?: string;
  nzIconTheme?: 'outline' | 'fill' | 'twotone';
  nzBadgeIconTheme?: 'outline' | 'fill' | 'twotone';
  nzIconType?: string;
  nzBadgeIconType?: string;
  placeholder?: string;
  profileImageField?: string;
  fullNameField?: string;
}
