<!-- Profile View -->
<div class="profile-section" *ngIf="!showRechargeView">
  <!-- Avatar -->
  <div class="avatar-wrapper">
    <div class="avatar-container">
      <nz-avatar
        [nzSize]="96"
        [nzSrc]="profileImageUrl || 'assets/default-avatar.png'"
        nzIcon="user"
      ></nz-avatar>
      <button
        nz-button
        nzShape="circle"
        nzType="default"
        class="camera-button"
        (click)="triggerFileInput()"
      >
        <i nz-icon nzType="camera"></i>
      </button>
      <input
        type="file"
        id="profileImageInput"
        style="display: none"
        (change)="onFileSelected($event)"
      />
    </div>
  </div>

  <!-- Actions -->
  <div class="action-buttons">
    <button nz-button nzType="primary" nzGhost (click)="changePassword()">
      <i nz-icon nzType="setting"></i> Change Password
    </button>
    <button
      nz-button
      nzType="primary"
      nzGhost
      *ngIf="getCurrentUser().roleId === enumUserRole.COMPANY_ADMIN"
    >
      <i nz-icon nzType="team"></i> Manage Users
    </button>
  </div>

  <!-- Main Info Card with Inline More Details -->
  <nz-card [nzBordered]="false" class="info-card">
    <button
      nz-button
      nzShape="circle"
      nzType="text"
      nzSize="small"
      class="edit-icon"
      (click)="isEditMode ? cancelEdit() : onEditProfile()"
    >
      <i nz-icon [nzType]="isEditMode ? 'close' : 'edit'"></i>
    </button>

    <!-- View Mode -->
    <div *ngIf="!isEditMode">
      <p><strong>Name</strong> {{ getCurrentUser().firstName }}</p>
      <p><strong>Role</strong> {{ getCurrentUser().roleDisplayText }}</p>
      <p *ngIf="getCurrentUser().roleId !== enumUserRole.PLATFORM_ADMIN">
        <strong>Contact</strong> {{ getCurrentUser().contactNumber }}
      </p>
      <p><strong>Email</strong> {{ getCurrentUser().email }}</p>
      <p>
        <strong>Address</strong>
        {{ getCurrentUser().primaryAddress?.addressLine1 }}
      </p>

      <!-- Expand Toggle (Centered Arrow) -->
      <div
        *ngIf="
          getCurrentUser().roleId !== enumUserRole.PLATFORM_ADMIN &&
          getCurrentUser().roleId !== enumUserRole.INDEPENDEDNT_AGENT
        "
        class="d-flex justify-content-center mt-3"
      >
        <button
          nz-button
          nzType="text"
          (click)="toggleMoreDetails()"
          class="more-details-toggle"
        >
          <i
            nz-icon
            [nzType]="showMoreDetails ? 'up' : 'down'"
            nzTheme="outline"
          ></i>
        </button>
      </div>

      <!-- Inline More Details -->
      <div *ngIf="showMoreDetails" class="pt-3">
        <p>
          <strong>Billing Address:</strong>
          {{ getCurrentUser().billingAddress?.addressLine1 || 'N/A' }}
        </p>
        <p>
          <strong>Company Id:</strong>
          {{ getCurrentUser().companyId || 'N/A' }}
        </p>
      </div>
    </div>

    <!-- Edit Mode -->
    <div *ngIf="isEditMode" class="edit-form">
      <div class="form-field">
        <label class="field-label">Name</label>
        <input
          nz-input
          [(ngModel)]="editUserData.firstName"
          placeholder="Enter your name"
          class="edit-input"
        />
      </div>

      <div class="form-field">
        <label class="field-label">Role</label>
        <input
          nz-input
          [value]="editUserData.displayText"
          class="edit-input"
          readonly
          disabled
        />
      </div>

      <div
        class="form-field"
        *ngIf="getCurrentUser().roleId !== enumUserRole.PLATFORM_ADMIN"
      >
        <label class="field-label">Contact</label>
        <input
          nz-input
          [(ngModel)]="editUserData.contactNumber"
          placeholder="Enter contact number"
          class="edit-input"
        />
      </div>

      <div class="form-field">
        <label class="field-label">Email</label>
        <input
          nz-input
          [value]="editUserData.email"
          class="edit-input"
          readonly
          disabled
        />
      </div>

      <div class="form-field">
        <label class="field-label">Address</label>
        <input
          nz-input
          [(ngModel)]="editUserData.primaryAddress.addressLine1"
          placeholder="Enter address"
          class="edit-input"
        />
      </div>

      <div class="edit-actions">
        <button
          nz-button
          nzType="primary"
          (click)="saveChanges()"
          [nzLoading]="isSaving"
          [disabled]="!hasUserEdited()"
        >
          Save
        </button>
        <button nz-button nzType="primary" nzGhost (click)="cancelEdit()">
          Cancel
        </button>
      </div>
    </div>
  </nz-card>

  <!-- Wallet Card -->
  <nz-card
    [nzBordered]="false"
    class="wallet-card"
    *ngIf="getCurrentUser().roleId !== enumUserRole.PLATFORM_ADMIN"
  >
    <div class="d-flex justify-content-between align-items-start mb-3">
      <h4 class="fw-bold mb-0">Wallet Details</h4>
      <button
        nz-button
        nzType="primary"
        class="recharge-button"
        (click)="rechargeWallet()"
      >
        Recharge Now
      </button>
    </div>

    <div class="row wallet-info">
      <div class="col">
        <p class="mb-0 fw-semibold">Wallet Balance</p>
        <p class="text-primary fw-bold">${{ walletBalance.toFixed(2) }}</p>
      </div>
      <div class="col">
        <p class="mb-0 fw-semibold">Last Recharge</p>
        <p class="fw-bold">{{ lastRechargeDate() }}</p>
      </div>
    </div>
  </nz-card>
</div>

<!-- Recharge View -->
<div class="recharge-section" *ngIf="showRechargeView">
  <div class="drawer-breadcrumb">
    <nz-breadcrumb>
      <nz-breadcrumb-item>
        <a (click)="goBackToProfile()">
          <span>My Profile</span>
        </a>
      </nz-breadcrumb-item>
      <nz-breadcrumb-item>
        <span>Recharge</span>
      </nz-breadcrumb-item>
    </nz-breadcrumb>
  </div>

  <app-wallet
    [(amount)]="amount"
    [visibleWallet]="visibleProfileDrawer"
    (visibleWalletChange)="visibleProfileDrawerChange.emit($event)"
    (visibleWalletAfterDelay)="visibleProfileAfterDelay.emit($event)"
  ></app-wallet>
</div>
