.profile-section {
  padding: 12px;
  max-width: 100%;
  overflow: hidden;
}

.avatar-wrapper {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
}

.avatar-container {
  position: relative;
  display: inline-block;

  .camera-button {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
  justify-content: flex-end;
}

.info-card {
  background: #f5f9ff;
  margin-bottom: 16px;
  border-radius: 10px;
  padding: 1rem;
  position: relative;

  .edit-icon {
    position: absolute;
    top: 8px;
    right: 8px;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .edit-form {
    padding-top: 8px;

    .form-field {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
    }

    .field-label {
      font-weight: 600;
      font-size: 14px;
      color: #333;
      min-width: 120px;
      margin-bottom: 4px;
    }

    .edit-input {
      flex: 1;
      border-radius: 6px;
      border: 1px solid #d9d9d9;
      padding: 8px 12px;
      font-size: 14px;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      &:disabled {
        background-color: #f5f5f5;
        color: #999;
      }
    }

    .edit-actions {
      display: flex;
      gap: 8px;
      justify-content: flex-start;
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid #e8e8e8;
    }
  }
}

.wallet-card {
  background: #f5f9ff;
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 16px;

  .recharge-button {
    font-weight: 500;
  }
}

.recharge-section {
  padding: 0;
  max-width: 100%;
  overflow: hidden;
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.drawer-breadcrumb {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;

  ::ng-deep .ant-breadcrumb {
    font-size: 14px;

    .ant-breadcrumb-link {
      color: #666;
      cursor: pointer;

      &:hover {
        color: #1890ff;
      }

      i {
        margin-right: 6px;
      }
    }

    .ant-breadcrumb-separator {
      color: #999;
    }
  }
}

.drawer-body {
  padding: 24px;
  background-color: #fafafa;
}

@media (max-width: 768px) {
  .drawer-header {
    padding: 16px;
  }

  .drawer-breadcrumb {
    padding: 8px 12px;
  }

  .info-card .edit-form .edit-actions {
    flex-direction: column;

    button {
      width: 100%;
      justify-content: center;
    }
  }
}
.more-details-toggle {
  padding: 0;
  margin: 0 auto;
  display: block;
  i {
    font-size: 10px;
  }
}
