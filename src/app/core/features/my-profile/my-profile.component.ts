import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';

import { UserService } from '../../services/user.service';
import { RechargeService } from '../../services/recharge.service';
import { NotificationService } from '../../services/notification.service';
import { TransactionService } from '../../services/transaction.service';

import {
  UserResponseDTO,
  TransactionDTO,
  UserRequestDTO,
  AddressRequestDTO,
  AddressResponseDTO,
} from '../../../api-client';
import { EnumUserRole } from '../../enumerations/user-roles';
import { EnumTransactionTypes } from '../../enumerations/transaction-types';

import {
  COMMON_STRINGS,
  DEFAULT_RECHARGE_AMOUNT,
  PHONE_REGEX,
  EMAIL_REGEX,
} from '../../constants/common';

import { WalletComponent } from '../wallet/wallet.component';

interface EditableUserData {
  firstName: string;
  roleName: string;
  contactNumber: string;
  email: string;
  primaryAddress: AddressRequestDTO;
  displayText: string;
}

@Component({
  selector: 'app-my-profile',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzAvatarModule,
    NzInputModule,
    NzBreadCrumbModule,
    WalletComponent,
  ],
  templateUrl: './my-profile.component.html',
  styleUrls: ['./my-profile.component.scss'],
})
export class MyProfileComponent implements OnInit, OnDestroy {
  @Input() profileImageUrl: string | null = null;
  @Input() visibleProfileDrawer = false;
  @Output() closeProfileDrawer = new EventEmitter<void>();
  @Output() visibleProfileDrawerChange = new EventEmitter<boolean>();
  @Output() visibleProfileAfterDelay = new EventEmitter<number>();

  readonly enumUserRole = EnumUserRole;

  walletBalance = 0;
  amount = DEFAULT_RECHARGE_AMOUNT;
  isEditMode = false;
  isSaving = false;
  showRechargeView = false;
  userId?: number;
  rechargeTransactions: TransactionDTO[] = [];

  editUserData: EditableUserData = this.createEmptyUserData();
  private originalUserData: EditableUserData = this.createEmptyUserData();

  constructor(
    private userService: UserService,
    private rechargeService: RechargeService,
    private notificationService: NotificationService,
    private transactionService: TransactionService,
    private router: Router,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.initializeUser();
    this.loadWalletBalance();
    this.loadRechargeTransactions();
    this.initializeUserData();
  }

  ngOnDestroy(): void {
    this.visibleProfileDrawerChange.emit(false);
  }

  closeProfile(): void {
    this.visibleProfileDrawerChange.emit(!this.visibleProfileDrawer);
  }

  getCurrentUser(): UserResponseDTO {
    const userInfo = this.userService.userInfo;
    return {
      firstName: userInfo?.firstName,
      roleName: userInfo?.roleName ?? 'N/A',
      contactNumber: userInfo?.contactNumber ?? 'N/A',
      roleId: userInfo?.roleId,
      primaryAddress: userInfo?.primaryAddress,
      billingAddress: userInfo?.billingAddress,
      email: userInfo?.email ?? 'N/A',
      roleDisplayText: userInfo?.roleDisplayText ?? 'N/A',
      companyId: userInfo?.companyId,
      userType: userInfo?.userType ?? 'N/A',
    };
  }

  onEditProfile(): void {
    this.isEditMode = true;
    this.editUserData = { ...this.originalUserData };
  }

  cancelEdit(): void {
    this.isEditMode = false;
    this.isSaving = false;
    this.editUserData = { ...this.originalUserData };
  }

  saveChanges(): void {
    if (!this.validateUserData()) return;

    if (!this.hasUserDataChanged()) {
      this.notificationService.infoMessage(
        COMMON_STRINGS.warningMessages.noChangesFound,
      );
      this.isEditMode = false;
      return;
    }

    this.isSaving = true;
    const userUpdateRequest = this.buildUserUpdateRequest();
    this.updateUser(userUpdateRequest);
  }

  hasUserEdited(): boolean {
    return this.hasUserDataChanged();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      const file = input.files[0];
      this.profileImageUrl = URL.createObjectURL(file);
    }
  }

  triggerFileInput(): void {
    const fileInput = document.getElementById(
      'profileImageInput',
    ) as HTMLInputElement;
    fileInput?.click();
  }

  rechargeWallet(): void {
    this.showRechargeView = true;
  }

  goBackToProfile(): void {
    this.showRechargeView = false;
  }

  onRechargeViewTimeout(delay: number): void {
    const currentRoute = this.router.url;
    setTimeout(() => {
      this.router.navigate([currentRoute]).then(() => {
        this.showRechargeView = true;
      });
    }, delay);
  }

  private async initializeUser(): Promise<void> {
    try {
      const userInfo = await this.userService.getUserRole();
      this.userId = userInfo?.id;
      if (!this.userId) {
        this.handleError(COMMON_STRINGS.errorMessages.handleUserError);
      }
    } catch {
      this.handleError(COMMON_STRINGS.errorMessages.handleUserError);
    }
  }

  private initializeUserData(): void {
    const currentUser = this.getCurrentUser();
    this.originalUserData = {
      firstName: currentUser.firstName || '',
      roleName: currentUser.roleName || '',
      contactNumber: currentUser.contactNumber || '',
      email: currentUser.email || '',
      primaryAddress: this.convertToAddressRequest(currentUser.primaryAddress),
      displayText: currentUser.roleDisplayText || '',
    };
    this.editUserData = { ...this.originalUserData };
  }

  private createEmptyUserData(): EditableUserData {
    return {
      firstName: '',
      roleName: '',
      contactNumber: '',
      email: '',
      displayText: '',
      primaryAddress: {
        addressLine1: '',
        addressLine2: '',
        suburb: '',
        stateId: 0,
        zipCodeId: 0,
      },
    };
  }

  private convertToAddressRequest(
    address: AddressResponseDTO | undefined,
  ): AddressRequestDTO {
    return {
      addressLine1: address?.addressLine1 || '',
      addressLine2: address?.addressLine2 || '',
      suburb: address?.suburb || '',
      stateId: address?.stateId || 0,
      zipCodeId: address?.zipCodeId || 0,
    };
  }

  private validateUserData(): boolean {
    const errors: string[] = [];
    const { firstName, contactNumber, email, primaryAddress } =
      this.editUserData;
    const original = this.originalUserData;

    if (firstName.trim() !== original.firstName && !firstName.trim()) {
      errors.push(COMMON_STRINGS.errorMessages.noNameChange);
    }

    if (contactNumber.trim() !== original.contactNumber) {
      if (!contactNumber.trim()) {
        errors.push(COMMON_STRINGS.errorMessages.noContactNumberChange);
      } else if (!PHONE_REGEX.test(contactNumber)) {
        errors.push(COMMON_STRINGS.errorMessages.invalidContactNumber);
      }
    }

    if (email.trim() !== original.email) {
      if (!email.trim()) {
        errors.push(COMMON_STRINGS.errorMessages.noEmailChange);
      } else if (!EMAIL_REGEX.test(email)) {
        errors.push(COMMON_STRINGS.errorMessages.invalidEmail);
      }
    }

    if (
      primaryAddress.addressLine1.trim() !==
        original.primaryAddress.addressLine1 &&
      !primaryAddress.addressLine1.trim()
    ) {
      errors.push(COMMON_STRINGS.errorMessages.noAddressChange);
    }

    if (errors.length) {
      this.notificationService.errorMessage(errors.join('\n'));
      return false;
    }

    return true;
  }

  private hasUserDataChanged(): boolean {
    const { firstName, contactNumber, email, primaryAddress } =
      this.editUserData;
    const original = this.originalUserData;

    return (
      firstName.trim() !== original.firstName ||
      contactNumber.trim() !== original.contactNumber ||
      email.trim() !== original.email ||
      primaryAddress.addressLine1.trim() !==
        original.primaryAddress.addressLine1
    );
  }

  private buildUserUpdateRequest(): UserRequestDTO {
    const userInfo = this.userService.userInfo;
    return {
      firstName: this.editUserData.firstName.trim(),
      contactNumber: this.editUserData.contactNumber.trim(),
      email: this.editUserData.email.trim(),
      lastName: userInfo?.lastName || '',
      roleId: userInfo?.roleId || 0,
      password: '',
      userType: UserRequestDTO.UserTypeEnum.Individual,
      primaryAddress: this.editUserData.primaryAddress,
      billingAddress: this.convertToAddressRequest(userInfo?.billingAddress),
    };
  }

  private updateUser(userRequestDTO: UserRequestDTO): void {
    if (!this.userId) {
      this.handleError(COMMON_STRINGS.errorMessages.handleUserError);
      return;
    }

    this.userService.updateUser(this.userId, userRequestDTO).subscribe({
      next: (response) => {
        this.isSaving = false;
        this.isEditMode = false;
        this.notificationService.successMessage(
          COMMON_STRINGS.successMessages.userUpdateSuccess,
        );
        this.userService.userInfo = response.data;
        this.initializeUserData();
      },
      error: () => {
        this.isSaving = false;
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToUpdateUser,
        );
      },
    });
  }

  private loadWalletBalance(): void {
    if (!this.userId) return;

    this.rechargeService.getUserWalletBalance(this.userId).subscribe({
      next: (balance) => (this.walletBalance = balance),
      error: () => {
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToFetchWalletBalance,
        );
      },
    });
  }

  private loadRechargeTransactions(): void {
    this.transactionService.getAllTransactions().subscribe({
      next: (transactions) => {
        this.rechargeTransactions = transactions.filter(
          (txn) => txn.transactionType === EnumTransactionTypes.RECHARGE,
        );
      },
      error: () => {
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToFetchTransactions,
        );
      },
    });
  }

  lastRechargeDate(): string {
    if (!this.rechargeTransactions.length) return '';

    const sortedTransactions = [...this.rechargeTransactions].sort(
      (a, b) =>
        new Date(b.transactionDate ?? 0).getTime() -
        new Date(a.transactionDate ?? 0).getTime(),
    );

    const date = sortedTransactions[0]?.transactionDate
      ? new Date(sortedTransactions[0].transactionDate)
      : new Date();

    return date.toISOString().split('T')[0];
  }

  private handleError(message: string): void {
    this.notificationService.error(message);
  }

  changePassword(): void {
    const email = this.userService.userInfo?.email;
    if (!email) {
      this.notificationService.error(
        COMMON_STRINGS.errorMessages.noEmailAvailable,
      );
      return;
    }
    this.userService
      .changePassword(this.userService.userInfo?.email || '')
      .subscribe({
        next: () => {
          this.notificationService.successMessage(
            COMMON_STRINGS.successMessages.passwordUpdateEmailSentSuccessfully,
          );
        },
        error: () => {
          this.notificationService.error(
            COMMON_STRINGS.errorMessages.failedToSendPasswordUpdateEmail,
          );
        },
      });
  }
  showMoreDetails = false;

  toggleMoreDetails(): void {
    this.showMoreDetails = !this.showMoreDetails;
  }
}
