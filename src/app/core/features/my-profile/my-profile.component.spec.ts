import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

import { UserService } from '../../services/user.service';
import { RechargeService } from '../../services/recharge.service';
import { TransactionService } from '../../services/transaction.service';
import { NotificationService } from '../../services/notification.service';
import { MyProfileComponent } from './my-profile.component';

describe('MyProfileComponent', () => {
  let component: MyProfileComponent;
  let fixture: ComponentFixture<MyProfileComponent>;
  let userServiceMock: jasmine.SpyObj<UserService>;
  let rechargeServiceMock: jasmine.SpyObj<RechargeService>;
  let transactionServiceMock: jasmine.SpyObj<TransactionService>;
  let notificationServiceMock: jasmine.SpyObj<NotificationService>;
  let activatedRouteMock: Partial<ActivatedRoute>;

  beforeEach(fakeAsync(async () => {
    userServiceMock = jasmine.createSpyObj('UserService', [
      'getUserRole',
      'updateUser',
    ]);
    rechargeServiceMock = jasmine.createSpyObj('RechargeService', [
      'getUserWalletBalance',
      'rechargeWallet',
    ]);
    transactionServiceMock = jasmine.createSpyObj('TransactionService', [
      'getAllTransactions',
    ]);
    notificationServiceMock = jasmine.createSpyObj('NotificationService', [
      'successMessage',
      'error',
      'showLoaderMessage',
      'hideLoaderMessage',
    ]);
    activatedRouteMock = {
      queryParams: of({ paymentAmount: 100, transactionId: 1 }),
    };

    await TestBed.configureTestingModule({
      imports: [MyProfileComponent, HttpClientTestingModule],
      providers: [
        { provide: UserService, useValue: userServiceMock },
        { provide: RechargeService, useValue: rechargeServiceMock },
        { provide: TransactionService, useValue: transactionServiceMock },
        { provide: NotificationService, useValue: notificationServiceMock },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
        HttpClient, // Provide HttpClient
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MyProfileComponent);
    component = fixture.componentInstance;

    // Mock service responses
    userServiceMock.getUserRole.and.returnValue(
      Promise.resolve({
        id: 1,
        firstName: 'John',
        roleName: 'INDIVIDUAL_AGENT',
      }),
    );
    rechargeServiceMock.getUserWalletBalance.and.returnValue(of(100));
    transactionServiceMock.getAllTransactions.and.returnValue(of([]));

    fixture.detectChanges();
    tick();
  }));

  it('should create', fakeAsync(() => {
    expect(component).toBeTruthy();
    tick();
  }));

  it('should load wallet balance on initialization', fakeAsync(() => {
    tick();
    expect(component.walletBalance).toBe(100);
    expect(rechargeServiceMock.getUserWalletBalance).toHaveBeenCalledWith(1);
  }));
});
