.mock-payment {
  padding: 24px;

  .backDropWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.25);
    top: 0;
    left: 0;
    right: 0;
    z-index: 10000;
    bottom: 0;
  }

  .status-card {
    max-width: 420px;
    width: 100%;
    text-align: center;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    border-radius: 12px;
  }

  .icon-wrapper {
    margin-bottom: 16px;

    .payment-icon {
      font-size: 48px;
      color: #1890ff;
    }
  }

  .status-title {
    font-size: 22px;
    font-weight: 600;
    color: #1f1f1f;
    margin-bottom: 8px;
  }

  .status-subtitle {
    font-size: 14px;
    color: #595959;
    margin-bottom: 20px;
  }

  .amount-display {
    font-size: 28px;
    font-weight: 600;
    color: #262626;
    margin-top: 10px;
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    font-size: 16px;
    border-bottom: 1px solid #f0f0f0;

    .label {
      color: #8c8c8c;
    }

    .value {
      font-weight: 500;
      color: #1f1f1f;
    }

    &:last-child {
      border-bottom: none;
    }
  }
}
