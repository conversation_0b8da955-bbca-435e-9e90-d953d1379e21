import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { MockPaymentComponent } from './mock-payment.component';
import { TransactionState } from '../../enumerations/transaction-state.enum';

describe('MockPaymentComponent', () => {
  let component: MockPaymentComponent;
  let fixture: ComponentFixture<MockPaymentComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MockPaymentComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ paymentAmount: 150 }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MockPaymentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize payment amount from query params', () => {
    expect(component.paymentAmount).toBe(150);
  });

  it('should show processing state initially', () => {
    expect(component.currentState).toBe(TransactionState.PROCESSING);
  });

  it('should return transaction ID', () => {
    expect(component.getTransactionId()).toBe(component.transactionId);
  });
});
