import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { NgF<PERSON>, CurrencyPipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';

import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';

import { RechargeService } from '../../services/recharge.service';
import { NotificationService } from '../../services/notification.service';
import { UserService } from '../../services/user.service';

import { WalletRechargeResponseDTO } from '../../../api-client';
import { COMMON_STRINGS, PRESET_AMOUNT } from '../../constants/common';
import { ROUTES } from '../../constants/routes';

@Component({
  selector: 'app-wallet',
  standalone: true,
  imports: [
    NgFor,
    FormsModule,
    CurrencyPipe,
    NzInputNumberModule,
    NzButtonModule,
    NzIconModule,
  ],
  templateUrl: './wallet.component.html',
  styleUrls: ['./wallet.component.scss'],
})
export class WalletComponent implements OnInit {
  // Inputs and Outputs
  @Input() amount = 0;
  @Input() visibleWallet = false;

  @Output() amountChange = new EventEmitter<number>();
  @Output() visibleWalletChange = new EventEmitter<boolean>();
  @Output() visibleWalletAfterDelay = new EventEmitter<number>();

  // Internal variables
  presetAmounts = PRESET_AMOUNT;
  private userId?: number;
  private walletBalance = 0;

  constructor(
    private rechargeService: RechargeService,
    private notificationService: NotificationService,
    private userService: UserService,
    private router: Router,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.initializeUser();
  }

  setPresetAmount(value: number): void {
    this.amount = value;
    this.amountChange.emit(value);
  }

  confirmRecharge(): void {
    if (!this.isUserAuthenticated()) return;

    const loadingMsgId = this.notificationService.showLoaderMessage(
      COMMON_STRINGS.loadingMessage.rechargeWallet,
    );

    this.rechargeService.rechargeWallet(this.userId!, this.amount).subscribe({
      next: (res) => this.handleRechargeSuccess(res, loadingMsgId),
      error: (err) => this.handleRechargeError(err, loadingMsgId),
    });

    this.handlePostRechargeNavigation();
  }

  onCancel(): void {
    this.closeWalletWithDelay();
  }

  getCurrentUser(): string {
    return this.userService.userInfo?.firstName ?? 'John Doe';
  }

  // ---------------------------
  // Private Helper Functions
  // ---------------------------

  private async initializeUser(): Promise<void> {
    try {
      const userInfo = await this.userService.getUserRole();
      if (userInfo?.id) {
        this.userId = userInfo.id;
      } else {
        this.handleUserError(COMMON_STRINGS.errorMessages.handleUserError);
      }
    } catch {
      this.handleUserError(COMMON_STRINGS.errorMessages.handleUserError);
    }
  }

  private isUserAuthenticated(): boolean {
    if (!this.userId) {
      this.handleUserError(COMMON_STRINGS.errorMessages.handleUserError);
      return false;
    }
    return true;
  }

  private handleRechargeSuccess(
    response: WalletRechargeResponseDTO,
    loadingMsgId: string,
  ): void {
    this.notificationService.hideLoaderMessage(loadingMsgId);
    this.walletBalance = response.newBalance || this.walletBalance;
    this.closeWalletWithDelay();
  }

  private handleRechargeError(
    err: HttpErrorResponse,
    loadingMsgId: string,
  ): void {
    this.notificationService.hideLoaderMessage(loadingMsgId);
    this.notificationService.error(
      COMMON_STRINGS.errorMessages.failedToRechargeWallet,
    );
  }

  private handleUserError(message: string): void {
    this.notificationService.error(message);
  }

  private handlePostRechargeNavigation(): void {
    const currentRoute = this.router.url;

    this.router.navigate([ROUTES.mockRecharge], {
      queryParams: { paymentAmount: this.amount },
    });

    this.closeWalletWithDelay();

    setTimeout(() => {
      this.router.navigate([currentRoute]);
    }, 10000);
  }

  private closeWalletWithDelay(): void {
    this.visibleWalletAfterDelay.emit(10000);
    this.visibleWalletChange.emit(false);
  }
}
