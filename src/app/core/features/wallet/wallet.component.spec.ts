import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { WalletComponent } from './wallet.component';
import { of, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { RechargeService } from '../../services/recharge.service';
import { NotificationService } from '../../services/notification.service';
import { UserService } from '../../services/user.service';
import { WalletRechargeResponseDTO } from '../../../api-client';

describe('WalletComponent', () => {
  let component: WalletComponent;
  let fixture: ComponentFixture<WalletComponent>;

  let mockRechargeService: jasmine.SpyObj<RechargeService>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockUserService: jasmine.SpyObj<UserService>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    mockRechargeService = jasmine.createSpyObj('RechargeService', [
      'rechargeWallet',
    ]);
    mockNotificationService = jasmine.createSpyObj('NotificationService', [
      'showLoaderMessage',
      'hideLoaderMessage',
      'error',
    ]);
    mockUserService = jasmine.createSpyObj('UserService', ['getUserRole'], {
      userInfo: { firstName: 'TestUser' },
    });
    mockRouter = jasmine.createSpyObj('Router', ['navigate'], { url: '/mock' });

    await TestBed.configureTestingModule({
      imports: [WalletComponent],
      providers: [
        { provide: RechargeService, useValue: mockRechargeService },
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: UserService, useValue: mockUserService },
        { provide: Router, useValue: mockRouter },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(WalletComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create WalletComponent', () => {
    expect(component).toBeTruthy();
  });

  it('should emit amountChange when setPresetAmount is called', () => {
    spyOn(component.amountChange, 'emit');
    component.setPresetAmount(1500);
    expect(component.amount).toBe(1500);
    expect(component.amountChange.emit).toHaveBeenCalledWith(1500);
  });

  it("should return current user's first name", () => {
    expect(component.getCurrentUser()).toBe('TestUser');
  });

  it('should handle successful recharge', fakeAsync(() => {
    component['userId'] = 1;
    component.amount = 1000;

    const mockResponse: WalletRechargeResponseDTO = { newBalance: 3000 };
    mockRechargeService.rechargeWallet.and.returnValue(of(mockResponse));
    mockNotificationService.showLoaderMessage.and.returnValue('loading123');

    spyOn(component['visibleWalletChange'], 'emit');
    spyOn(component['visibleWalletAfterDelay'], 'emit');

    component.confirmRecharge();
    tick(10000);

    expect(mockRechargeService.rechargeWallet).toHaveBeenCalledWith(1, 1000);
    expect(mockNotificationService.hideLoaderMessage).toHaveBeenCalledWith(
      'loading123',
    );
    expect(component['visibleWalletChange'].emit).toHaveBeenCalledWith(false);
    expect(component['visibleWalletAfterDelay'].emit).toHaveBeenCalledWith(
      10000,
    );
    expect(mockRouter.navigate).toHaveBeenCalledTimes(2);
  }));

  it('should handle recharge error', () => {
    component['userId'] = 1;
    component.amount = 1000;

    mockRechargeService.rechargeWallet.and.returnValue(
      throwError(() => new Error('Failed')),
    );
    mockNotificationService.showLoaderMessage.and.returnValue('loading123');

    component.confirmRecharge();

    expect(mockNotificationService.error).toHaveBeenCalled();
  });

  it('should emit cancel properly', () => {
    spyOn(component.visibleWalletChange, 'emit');
    spyOn(component.visibleWalletAfterDelay, 'emit');
    component.onCancel();
    expect(component.visibleWallet).toBeFalse();
    expect(component.visibleWalletChange.emit).toHaveBeenCalledWith(false);
    expect(component.visibleWalletAfterDelay.emit).toHaveBeenCalledWith(10000);
  });
});
