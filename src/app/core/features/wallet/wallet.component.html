<div class="recharge-section">
  <!-- Drawer Header -->
  <div class="drawer-header">
    <div class="user-info">
      <i class="fas fa-wallet wallet-icon"></i>
      <div class="user-details">
        <div class="greeting">Hi,{{ getCurrentUser() }}</div>
        <div class="subtext">Recharge your wallet</div>
      </div>
    </div>
  </div>

  <!-- Recharge Form -->
  <div class="drawer-body recharge-container">
    <label class="field-label">Enter Amount</label>

    <nz-input-number
      class="recharge-input"
      [(ngModel)]="amount"
      (ngModelChange)="amountChange.emit(amount)"
    >
      <span nzInputPrefix>$</span>
    </nz-input-number>
    <!-- Preset Buttons -->
    <div class="preset-buttons">
      <button
        *ngFor="let preset of presetAmounts"
        nz-button
        nzType="default"
        class="preset-button"
        (click)="setPresetAmount(preset)"
      >
        {{ preset | currency: 'USD' }}
      </button>
    </div>

    <!-- Confirm & Cancel -->
    <div class="action-buttons">
      <button
        nz-button
        nzType="primary"
        block
        class="confirm-button"
        (click)="confirmRecharge()"
      >
        <i class="fas fa-check-circle"></i> Confirm Recharge
        {{ amount | currency: 'USD' }}
      </button>

      <button
        nz-button
        nzType="primary"
        nzGhost
        block
        class="cancel-button"
        (click)="onCancel()"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
