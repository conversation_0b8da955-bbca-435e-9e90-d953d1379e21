.recharge-section {
    padding: 0;
    max-width: 100%;
    overflow: hidden;
    animation: slideInRight 0.3s ease-in-out;
  }
  
  @keyframes slideInRight {
    from {
      transform: translateX(0);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .drawer-header {
    background: var(--primary-button-color, #1890ff);
    padding: 24px;
    color: white;
    border-bottom: 1px solid #e6f7ff;
  
    .user-info {
      display: flex;
      align-items: center;
      gap: 16px;
  
      .wallet-icon {
        font-size: 32px;
      }
  
      .user-details {
        .greeting {
          font-size: 16px;
          font-weight: 600;
        }
  
        .username {
          font-weight: 700;
          font-size: 18px;
        }
  
        .subtext {
          font-size: 12px;
          color: #e6f7ff;
        }
      }
    }
  }
  
  .drawer-body {
    padding: 24px;
    background-color: #fafafa;
  }
  
  .recharge-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .field-label {
    font-weight: 600;
    font-size: 14px;
    color: #333;
  }
  
  .preset-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    width: 100%;
  
    .preset-button {
      border-radius: 8px;
      background-color: #f0f2f5;
      border: none;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      padding: 8px 12px;
      text-align: center;
      min-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
  
      &:hover {
        background-color: #e6f7ff;
        border: 1px solid #91d5ff;
      }
    }
  }
  .recharge-input {
    width: 100%;
    margin: 12px 0;
  }
  
  .action-buttons {
    display: flex;
    gap: 12px;
    width: 100%;
  }
  
  .confirm-button {
    border-radius: 8px;
    height: 40px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    flex: 1;
  }
  
  .cancel-button {
    border-radius: 8px;
    height: 40px;
    flex: 1;
  }
  
  @media (max-width: 768px) {
    .preset-buttons {
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }
    
    .drawer-header {
      padding: 16px;
      
      .user-info {
        .wallet-icon {
          font-size: 24px;
        }
      }
    }
  }
  
  @media (max-width: 480px) {
    .preset-buttons {
      grid-template-columns: 1fr;
      gap: 8px;
    }
  }
  