import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzResultModule } from 'ng-zorro-antd/result';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { TransactionDTO } from '../../../api-client';
import { transactionId } from '../../constants/common';
import { TransactionState } from '../../enumerations/transaction-state.enum';

@Component({
  selector: 'app-mock-recharge',
  standalone: true,
  imports: [CommonModule, NzSpinModule, NzCardModule, NzResultModule],
  templateUrl: './recharge-mock.component.html',
  styleUrls: ['./recharge-mock.component.scss'],
})
export class MockRechargeComponent implements OnInit {
  paymentAmount = 0;
  currentState: TransactionState = TransactionState.PROCESSING;
  transactionId = transactionId;

  isLoading = false;
  transactionData: TransactionDTO[] = [];
  currentTransaction: 'RECHARGE' | 'PURCHASE' = 'RECHARGE';

  constructor(private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.paymentAmount = params['paymentAmount'] || 0;
    });

    setTimeout(() => {
      this.currentState = TransactionState.SUCCESS;
    }, 3000);
  }

  getTransactionId(): number {
    return this.transactionId!;
  }
}
