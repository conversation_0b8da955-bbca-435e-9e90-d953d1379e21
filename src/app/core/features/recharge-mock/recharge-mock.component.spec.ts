import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MockRechargeComponent } from './recharge-mock.component';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { TransactionState } from '../../enumerations/transaction-state.enum';

describe('MockRechargeComponent', () => {
  let component: MockRechargeComponent;
  let fixture: ComponentFixture<MockRechargeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MockRechargeComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ paymentAmount: 200 }),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MockRechargeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with PROCESSING state', () => {
    expect(component.currentState).toBe(TransactionState.PROCESSING);
  });

  it('should set paymentAmount from query params', () => {
    expect(component.paymentAmount).toBe(200);
  });
});
