import * as MapEnum from '../enumerations/mapEnum';

export class MapOptions {
  public MapType: MapEnum.MapType;
  public MapId: string;
  public ZoomLevel: number;
  public MinZoom: number | null;
  public MaxZoom: number | null;
  public CenterLat: number;
  public CenterLng: number;
  public IsMapTypeChangable: boolean;
  public IsDraggable: boolean;
  public FullscreenControl: boolean;
  public RequireCtrlToZoom: boolean;
  public FeaturesToHide: MapEnum.MapFeatures[];

  constructor(mapId: string) {
    this.MapId = mapId;
    this.MapType = MapEnum.MapType.Roadmap;
    this.ZoomLevel = 5;
    this.MinZoom = 7;
    this.MaxZoom = null;
    this.CenterLat = 32.7568257;
    this.CenterLng = -97.0215761;
    this.IsMapTypeChangable = false;
    this.IsDraggable = true;
    this.FullscreenControl = true;
    this.RequireCtrlToZoom = true;
    this.FeaturesToHide = [];
  }

  SetAllOptions(
    mapType: MapEnum.MapType,
    zoomLevel: number,
    minZoom: number,
    maxZoom: number,
    centerLat: number,
    centerLng: number,
    isDraggable: boolean,
    isMapTypeChangable: boolean,
  ): void {
    this.MapType = mapType;
    this.ZoomLevel = zoomLevel;
    this.MinZoom = minZoom;
    this.MaxZoom = maxZoom;
    this.CenterLat = centerLat;
    this.CenterLng = centerLng;
    this.IsMapTypeChangable = isMapTypeChangable;
    this.IsDraggable = isDraggable;
  }

  SetBasicOptions(
    mapType: MapEnum.MapType,
    zoomLevel: number,
    minZoom: number | null,
    maxZoom: number | null,
    centerLat: number,
    centerLng: number,
  ): void {
    this.MapType = mapType;
    this.ZoomLevel = zoomLevel;
    this.MinZoom = minZoom;
    this.MaxZoom = maxZoom;
    this.CenterLat = centerLat;
    this.CenterLng = centerLng;
  }
}
