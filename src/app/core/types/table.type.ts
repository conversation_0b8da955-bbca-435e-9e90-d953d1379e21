export type TFilterFn<T> = (searchText: string, data: T) => boolean;

export type TDisableFn<T> = (rowData: T) => boolean;

export type TGetClassNameFn<T> = (rowData: T, isChild?: boolean) => string;

export type TGetToolTipTextFn<T> = (rowData: T) => string | null;

export type TGetIconTextFn<T> = (rowData: T) => string;

export type TGetTextFn<T> = (rowData: T) => string;

export type TLoadingRowFn<T> = (rowData: T) => boolean;

export type TGetBtnLabel<T> = (rowData: T) => string;

export type TGetCondition<T> = (rowData: T) => boolean;

export type TGetLabel<T> = (rowData: T) => string | null;

export type TNzToolTipPlacementTypes =
  | 'top'
  | 'left'
  | 'right'
  | 'bottom'
  | 'topLeft'
  | 'topRight'
  | 'bottomLeft'
  | 'bottomRight'
  | 'leftTop'
  | 'leftBottom'
  | 'rightTop'
  | 'rightBottom'
  | string[];
