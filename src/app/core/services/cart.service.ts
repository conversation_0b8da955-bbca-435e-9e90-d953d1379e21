import { Injectable } from '@angular/core';
import { map, Observable, Subject } from 'rxjs';
import {
  ApiResponseCartResponseDTO,
  ApiResponseOrderResponseDTO,
  CartRequestDTO,
  CartResponseDTO,
  OrderControllerService,
  OrderRequestDTO,
  OrderResponseDTO,
} from '../../api-client';

@Injectable({
  providedIn: 'root',
})
export class CartService {
  private readonly cartUpdated = new Subject<void>();

  constructor(private orderControllerService: OrderControllerService) {}

  addToCart(items: CartRequestDTO[]): Observable<CartResponseDTO> {
    return this.orderControllerService.addToCart(items).pipe(
      map((response: ApiResponseCartResponseDTO) => {
        const cartResponse = this.validateCartResponse(response);
        this.notifyCartUpdate();
        return cartResponse;
      }),
    );
  }

  getCart(): Observable<CartResponseDTO> {
    return this.orderControllerService
      .getCart()
      .pipe(
        map((response: ApiResponseCartResponseDTO) =>
          this.validateCartResponse(response),
        ),
      );
  }

  removeFromCart(itemId: number): Observable<CartResponseDTO> {
    return this.orderControllerService.removeFromCart(itemId).pipe(
      map((response: ApiResponseCartResponseDTO) => {
        const cartResponse = this.validateCartResponse(response);
        this.notifyCartUpdate();
        return cartResponse;
      }),
    );
  }

  createOrder(orderItemIds: number[]): Observable<OrderResponseDTO> {
    this.validateOrderItems(orderItemIds);

    const orderRequest: OrderRequestDTO = { orderItemIds };

    return this.orderControllerService.createOrder(orderRequest).pipe(
      map((response: ApiResponseOrderResponseDTO) => {
        const orderResponse = this.validateOrderResponse(response);
        this.notifyCartUpdate();
        return orderResponse;
      }),
    );
  }

  onCartUpdated(): Observable<void> {
    return this.cartUpdated.asObservable();
  }

  private validateCartResponse(
    response: ApiResponseCartResponseDTO,
  ): CartResponseDTO {
    if (!response.data) {
      throw new Error('Cart response is undefined');
    }
    return response.data;
  }

  private validateOrderResponse(
    response: ApiResponseOrderResponseDTO,
  ): OrderResponseDTO {
    if (!response.data) {
      throw new Error('Order response is undefined');
    }
    return response.data;
  }

  private validateOrderItems(orderItemIds: number[]): void {
    if (!orderItemIds?.length) {
      throw new Error('Order item IDs cannot be empty');
    }
  }

  private notifyCartUpdate(): void {
    this.cartUpdated.next();
  }
}
