import { ElementRef, Injectable } from '@angular/core';
import { ITableColumn } from '../interface/table';
import { NzTableSortFn } from 'ng-zorro-antd/table';
import { EnumSortType } from '../enumerations/sort-type';

@Injectable({
  providedIn: 'root',
})
export class DataGridService {
  getLeafColumns<T>(columns: ITableColumn<T>[]): ITableColumn<T>[] {
    const leafColumns: ITableColumn<T>[] = [];

    const traverse = (cols: ITableColumn<T>[]) => {
      for (const col of cols) {
        if (col.children && col.children.length > 0) {
          // If the column has children, recursively traverse them
          traverse(col.children);
        } else {
          // If the column has no children, it's a leaf node
          leafColumns.push(col);
        }
      }
    };

    traverse(columns); // Start traversing from the root columns
    return leafColumns;
  }

  calculateColspanAndRowspan<T>(columns: ITableColumn<T>[]): ITableColumn<T>[] {
    let highestDepth = 0;

    // First pass: Find the highest depth in the column tree
    const findHighestDepth = (
      cols: ITableColumn<T>[],
      currentDepth: number,
    ): void => {
      for (const col of cols) {
        if (col.children && col.children.length > 0) {
          // If the column has children, recursively find the depth
          findHighestDepth(col.children, currentDepth + 1);
        } else {
          // If the column has no children, update the highest depth
          highestDepth = Math.max(highestDepth, currentDepth);
        }
      }
    };

    findHighestDepth(columns, 0); // Start traversing from the root columns

    // Second pass: Calculate colspan and rowspan
    const traverse = (
      cols: ITableColumn<T>[],
      currentDepth: number,
    ): number => {
      let totalColspan = 0;

      for (const col of cols) {
        if (col.children && col.children.length > 0) {
          // If the column has children, recursively calculate their colspan
          col.colspan = traverse(col.children, currentDepth + 1);
          col.rowspan = 1; // Parent columns have a rowspan of 1
          totalColspan += col.colspan;
        } else {
          // If the column has no children, it's a leaf node
          col.colspan = 1; // Leaf nodes have a colspan of 1
          col.rowspan = highestDepth - currentDepth + 1; // Calculate rowspan based on depth
          totalColspan += 1;
        }
      }

      return totalColspan;
    };

    traverse(columns, 0); // Start traversing from the root columns
    return columns;
  }

  // Function to calculate the total number of leaf nodes
  getTotalLeafNodes<T>(columns: ITableColumn<T>[]): number {
    let totalLeafNodes = 0;

    const traverse = (cols: ITableColumn<T>[]) => {
      for (const col of cols) {
        if (col.children && col.children.length > 0) {
          // If the column has children, recursively traverse them
          traverse(col.children);
        } else {
          // If the column has no children, it's a leaf node
          totalLeafNodes += 1;
        }
      }
    };

    traverse(columns); // Start traversing from the root columns
    return totalLeafNodes;
  }

  /**
   * Calculates the highest depth of the column tree.
   * @param columns The array of table columns.
   * @returns The highest depth of the tree.
   */
  getHighestDepth<T>(columns: ITableColumn<T>[]): number {
    const calculateDepth = (
      cols: ITableColumn<T>[],
      currentDepth: number,
    ): number => {
      let maxDepth = currentDepth;
      for (const col of cols) {
        if (col.children && col.children.length > 0) {
          // Recursively calculate depth for children
          maxDepth = Math.max(
            maxDepth,
            calculateDepth(col.children, currentDepth + 1),
          );
        }
      }
      return maxDepth;
    };

    return calculateDepth(columns, 1); // Start with depth 1 (root level)
  }

  /**
   * Resets the sortOrder for all leaf columns in the column tree.
   * @param columns The array of table columns.
   */
  resetSortOrder<T>(columns: ITableColumn<T>[]): ITableColumn<T>[] {
    const traverse = (cols: ITableColumn<T>[]): void => {
      for (const col of cols) {
        if (col.children && col.children.length > 0) {
          // If the column has children, recursively traverse them
          traverse(col.children);
        } else {
          // If the column has no children, reset sortOrder
          col.sortOrder = null;
        }
      }
    };

    traverse(columns); // Start traversing from the root columns
    return columns;
  }

  /**
   * Finds a column by its field value.
   * @param columns The array of table columns.
   * @param field The field value to search for.
   * @returns The column that matches the given field, or undefined if not found.
   */
  getColumnByField<T>(
    columns: ITableColumn<T>[],
    field: string | null,
  ): ITableColumn<T> | null {
    const traverse = (cols: ITableColumn<T>[]): ITableColumn<T> | null => {
      for (const col of cols) {
        if (col.field === field) {
          // If the column matches the field, return it
          return col;
        }
        // If the column has children, recursively search them
        if (col.children && col.children.length > 0) {
          const foundColumn = traverse(col.children);
          if (foundColumn) {
            return foundColumn;
          }
        }
      }
      return null; // Return null if no matching column is found
    };

    return traverse(columns); // Start searching from the root columns
  }

  scrollToRow(
    rowIndex: number,
    dataTable: ElementRef<HTMLElement>,
    afterDelay = 500,
  ): void {
    //  Use nativeElement to access the DOM element
    setTimeout(() => {
      const rowElement = dataTable.nativeElement?.getElementsByClassName(
        `row-${rowIndex}`,
      )[0];
      if (rowElement) {
        rowElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, afterDelay);
  }

  sortData<T>(
    sortColumn: ITableColumn<T> | null,
    sortOrder: EnumSortType | null,
    filteredData: T[],
  ): T[] {
    if (!sortOrder || !sortColumn) return [...filteredData];
    sortColumn.sortOrder = sortOrder;
    return [...filteredData].sort((a, b) => {
      const compareResult = (sortColumn.sortFn as NzTableSortFn<T>)(a, b);
      return sortOrder === EnumSortType.ascend ? compareResult : -compareResult;
    });
  }
}
