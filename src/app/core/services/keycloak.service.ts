import { Injectable } from '@angular/core';
import Keycloak from 'keycloak-js';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class KeycloakService {
  private keycloak!: Keycloak;

  init(): Promise<boolean> {
    this.keycloak = new Keycloak({
      url: environment.keycloak.url,
      realm: environment.keycloak.realm,
      clientId: environment.keycloak.clientId,
    });

    return this.keycloak
      .init({
        onLoad: 'login-required',
        checkLoginIframe: false,
        flow: 'standard',
        pkceMethod: 'S256',
      })
      .then((authenticated) => {
        if (authenticated) {
          this.storeTokens();
          this.setupTokenRefresh();
        }
        return authenticated;
      });
  }

  private storeTokens(): void {
    localStorage.setItem('access_token', this.keycloak.token ?? '');
    localStorage.setItem('refresh_token', this.keycloak.refreshToken ?? '');
  }

  private setupTokenRefresh(): void {
    this.keycloak.onTokenExpired = () => {
      this.keycloak
        .updateToken(60)
        .then((refreshed) => {
          if (refreshed) {
            this.storeTokens();
          } else {
            console.warn('Token not refreshed, user needs to login');
          }
        })
        .catch(() => {
          console.error('Failed to refresh token');
          this.login();
        });
    };
  }

  login(redirectUri: string = window.location.origin + '/homepage'): void {
    this.keycloak
      .login({
        redirectUri: redirectUri,
      })
      .catch((err) => {
        console.error('Keycloak login failed', err);
      });
  }

  logout(redirectUri: string = window.location.origin + '/home'): void {
    this.keycloak
      .logout({
        redirectUri: redirectUri,
      })
      .catch((err) => {
        console.error('Keycloak logout failed', err);
      });

    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  getUsername(): string | undefined {
    return this.keycloak.tokenParsed?.['preferred_username'];
  }

  isLoggedIn(): boolean {
    return !!this.getToken();
  }

  register(): void {
    this.keycloak.login({ action: 'register' }).catch((err) => {
      console.error('Keycloak registration redirect failed', err);
    });
  }

  getEmail(): string | undefined {
    return this.keycloak.tokenParsed?.['email'];
  }

  getName(): string | undefined {
    return this.keycloak.tokenParsed?.['name'];
  }
}
