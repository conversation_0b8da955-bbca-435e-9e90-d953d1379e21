import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  ApiResponseCompanyDTO,
  ApiResponsePageCompanyDTO,
  ApiResponsePageUserResponseDTO,
  ApiResponseUserResponseDTO,
  ApiResponseVoid,
  CompanyControllerService,
  CompanyRequestDTO,
  UserControllerService,
  UserRequestDTO,
} from '../../api-client';

@Injectable({
  providedIn: 'root',
})
export class RegisterService {
  constructor(
    private userService: UserControllerService,
    private companyService: CompanyControllerService,
  ) {}

  public createUser(
    userRequest: UserRequestDTO,
  ): Observable<ApiResponseUserResponseDTO> {
    return this.userService.createUser(userRequest);
  }

  public createCompany(
    companyRequest: CompanyRequestDTO,
  ): Observable<ApiResponseCompanyDTO> {
    return this.companyService.createCompany(companyRequest);
  }

  public getAllCompanies(): Observable<ApiResponsePageCompanyDTO> {
    return this.companyService.getCompanies();
  }

  public deleteCompany(companyId: number): Observable<ApiResponseVoid> {
    return this.companyService.deleteCompany(companyId);
  }

  public activeInactiveCompany(
    id: number,
    isActive: boolean,
  ): Observable<ApiResponseCompanyDTO> {
    return this.companyService.updateCompanyActiveStatus(id, isActive);
  }

  public updateCompany(
    id: number,
    companyRequestDTO: CompanyRequestDTO,
  ): Observable<ApiResponseCompanyDTO> {
    return this.companyService.updateCompany(id, companyRequestDTO);
  }

  public getAllUsers(
    companyId: number,
  ): Observable<ApiResponsePageUserResponseDTO> {
    return this.userService.getUsers(
      undefined,
      undefined,
      companyId,
      undefined,
      undefined,
      200,
      undefined,
      undefined,
      undefined,
    );
  }

  public activeInactiveUser(
    id: number,
    isActive: boolean,
  ): Observable<ApiResponseUserResponseDTO> {
    return this.userService.updateUserActiveStatus(id, isActive);
  }
}
