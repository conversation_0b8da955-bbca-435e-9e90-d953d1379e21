import { Injectable } from '@angular/core';
import { LatLng } from '../models/latLng';

@Injectable({
  providedIn: 'root',
})
export class MapHelperServiceService {
  private baseUrl = 'assets/images/googleMapPins/';

  GetNeutralPinUrl() {
    return this.baseUrl + 'marker_teal.png';
  }

  GetClickedPositionPin() {
    return this.baseUrl + 'marker_click.png';
  }

  GetLatLngListFromPolygon(polygonText: string): LatLng[] {
    const latlngList: LatLng[] = [];
    if (polygonText) {
      polygonText = polygonText.replace('POLYGON((', '');
      polygonText = polygonText.replace('))', '');
      polygonText.split(',').forEach((point) => {
        const latlng = new LatLng();
        latlng.Latitude = parseFloat(point.split(' ')[1]);
        latlng.Longitude = parseFloat(point.split(' ')[0]);
        latlngList.push(latlng);
      });
    }
    return latlngList;
  }
}
