// import { TestBed } from '@angular/core/testing';
// import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
// import { AuthService } from './auth.service';
// import { User } from '../models/user.model';
// import { IApiResponse } from '../models/api-response.model';
// import { HttpErrorResponse } from '@angular/common/http';
// import { StorageService } from '../services/storage.service';

// describe('AuthService', () => {
//   let service: AuthService;
//   let httpMock: HttpTestingController;
//   let storageService: StorageService;

//   const mockUser: User = {
//     id: 1,
//     username: 'testuser',
//     email: '<EMAIL>',
//     firstName: 'Test',
//     lastName: 'User',
//     role: 'user',
//     isActive: true,
//     createdAt: '2024-01-01T00:00:00.000Z',
//     updatedAt: '2024-01-01T00:00:00.000Z'
//   };

//   beforeEach(() => {
//     TestBed.configureTestingModule({
//       imports: [HttpClientTestingModule],
//       providers: [AuthService, StorageService]
//     });

//     service = TestBed.inject(AuthService);
//     httpMock = TestBed.inject(HttpTestingController);
//     storageService = TestBed.inject(StorageService);
//     storageService.clear(); // clear storage before each test
//   });

//   afterEach(() => {
//     httpMock.verify();
//     storageService.clear(); // clear storage after each test
//   });

//   it('should be created', () => {
//     expect(service).toBeTruthy();
//   });

//   it('should login successfully', () => {
//     const mockResponse: IApiResponse<User> = {
//       data: mockUser,
//       message: 'Login successful',
//       status: 200,
//       success: true,
//       timestamp: new Date().toISOString()
//     };

//     service.login('testuser', 'password').subscribe(response => {
//       expect(response).toEqual(mockResponse);
//       expect(service.isAuthenticated()).toBe(true);
//       expect(service.getCurrentUser()).toEqual(mockUser);
//     });

//     const req = httpMock.expectOne('http://localhost:3000/api/auth/login');
//     expect(req.request.method).toBe('POST');
//     req.flush(mockResponse);
//   });

//   it('should handle login failure', () => {
//     const mockErrorResponse = {
//       status: 401,
//       statusText: 'Unauthorized',
//       error: {
//         message: 'Invalid credentials'
//       }
//     };

//     service.login('testuser', 'wrongpassword').subscribe({
//       next: () => fail('Should have failed with a 401 error'),
//       error: (error: HttpErrorResponse) => {
//         expect(error.status).toBe(401);
//         expect(error.error.message).toBe('Invalid credentials');
//         expect(service.isAuthenticated()).toBe(false);
//         expect(service.getCurrentUser()).toBeNull();
//       }
//     });

//     const req = httpMock.expectOne('http://localhost:3000/api/auth/login');
//     req.flush(mockErrorResponse.error, mockErrorResponse);
//   });

//   it('should logout successfully', () => {
//     service.login('testuser', 'password').subscribe();
//     const loginReq = httpMock.expectOne('http://localhost:3000/api/auth/login');
//     loginReq.flush({
//       data: mockUser,
//       message: 'Login successful',
//       status: 200,
//       success: true,
//       timestamp: new Date().toISOString()
//     });

//     service.logout();
//     expect(service.isAuthenticated()).toBe(false);
//     expect(service.getCurrentUser()).toBeNull();
//     expect(storageService.getItem('currentUser')).toBeNull();
//   });

//   it('should initialize with stored user', () => {
//     storageService.setItem('currentUser', mockUser);

//     TestBed.resetTestingModule();
//     TestBed.configureTestingModule({
//       imports: [HttpClientTestingModule],
//       providers: [AuthService, StorageService],
//     });

//     const newService = TestBed.inject(AuthService);
//     expect(newService.isAuthenticated()).toBeTrue();
//     expect(newService.getCurrentUser()).toEqual(mockUser);
//   });
// });
