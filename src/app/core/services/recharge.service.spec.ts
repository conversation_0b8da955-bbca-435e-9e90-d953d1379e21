// import { TestBed } from '@angular/core/testing';
// import { HttpClientTestingModule } from '@angular/common/http/testing';
// import { RechargeService } from './recharge.service';
// import { WalletControllerService, WalletRechargeResponseDTO, AccountDTO } from '../../api-client';
// import { of } from 'rxjs';

// describe('RechargeService', () => {
//   let service: RechargeService;
//   let walletControllerServiceSpy: jasmine.SpyObj<WalletControllerService>;

//   beforeEach(() => {
//     // Create a spy for WalletControllerService
//     const walletSpy = jasmine.createSpyObj('WalletControllerService', [
//       'rechargeWallet',
//       'getUserAccount'
//     ]);

//     TestBed.configureTestingModule({
//       imports: [
//         HttpClientTestingModule // Add HttpClientTestingModule
//       ],
//       providers: [
//         RechargeService,
//         { provide: WalletControllerService, useValue: walletSpy }
//       ]
//     });

//     service = TestBed.inject(RechargeService);
//     walletControllerServiceSpy = TestBed.inject(WalletControllerService) as jasmine.SpyObj<WalletControllerService>;
//   });

//   it('should be created', () => {
//     expect(service).toBeTruthy();
//   });

//   it('should recharge wallet with correct parameters', () => {
//     const userId = 1;
//     const amount = 100;
//     const expectedResponse: WalletRechargeResponseDTO = { newBalance: 200 };

//     // Use type assertion to match the expected return type
//     walletControllerServiceSpy.rechargeWallet.and.returnValue(of(expectedResponse) as any);

//     service.rechargeWallet(userId, amount).subscribe(response => {
//       expect(response).toEqual(expectedResponse);
//     });

//     expect(walletControllerServiceSpy.rechargeWallet).toHaveBeenCalledWith(
//       userId,
//       { amount: amount, currency: 'AUD' }
//     );
//   });

//   it('should get user wallet balance', () => {
//     const userId = 1;
//     const accountResponse: AccountDTO = { balance: 150 };

//     // Use type assertion to match the expected return type
//     walletControllerServiceSpy.getUserAccount.and.returnValue(of(accountResponse) as any);

//     service.getUserWalletBalance(userId).subscribe(balance => {
//       expect(balance).toEqual(150);
//     });

//     expect(walletControllerServiceSpy.getUserAccount).toHaveBeenCalledWith(userId);
//   });

//   it('should return 0 if balance is null or undefined', () => {
//     const userId = 1;
//     const accountResponse: AccountDTO = { balance: null };

//     // Use type assertion to match the expected return type
//     walletControllerServiceSpy.getUserAccount.and.returnValue(of(accountResponse) as any);

//     service.getUserWalletBalance(userId).subscribe(balance => {
//       expect(balance).toEqual(0);
//     });

//     expect(walletControllerServiceSpy.getUserAccount).toHaveBeenCalledWith(userId);
//   });
// });
