import { inject, Injectable } from '@angular/core';
import {
  NzNotificationContentType,
  NzNotificationService,
} from 'ng-zorro-antd/notification';
import { NzMessageContentType, NzMessageService } from 'ng-zorro-antd/message';
import { EnumNotificationMessageTypes } from '../enumerations/notificationMessage';

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  // Services
  private notificationService = inject(NzNotificationService);
  private messageService = inject(NzMessageService);

  // Private Functions

  /**
   * Displays a toast message (success, error, warning, info, or blank)
   * @param type Message type (Enum)
   * @param content Content of the message
   */
  private createMessage(
    type: EnumNotificationMessageTypes,
    content: NzMessageContentType,
  ): void {
    this.messageService.create(type, content);
  }

  /**
   * Displays a notification with a title and content
   * @param type Notification type (Enum)
   * @param content Content of the notification
   * @param title Title of the notification
   */
  private createNotification(
    type: EnumNotificationMessageTypes,
    content: NzNotificationContentType,
    title: string,
  ): void {
    this.notificationService.create(type, title, content);
  }

  /*********************  Notifications ****************************/

  /**
   * Display a success notification
   * Example: this.notificationService.success('Data saved successfully!');
   */
  success(content: NzNotificationContentType, title = 'Success') {
    this.createNotification(
      EnumNotificationMessageTypes.success,
      content,
      title,
    );
  }

  /**
   * Display an error notification
   * Example: this.notificationService.error('Failed to save data.');
   */
  error(content: NzNotificationContentType, title = 'Error') {
    this.createNotification(EnumNotificationMessageTypes.error, content, title);
  }

  /**
   * Display a warning notification
   * Example: this.notificationService.warning('Check your input before proceeding.');
   */
  warning(content: NzNotificationContentType, title = 'Warning') {
    this.createNotification(
      EnumNotificationMessageTypes.warning,
      content,
      title,
    );
  }

  /**
   * Display an info notification
   * Example: this.notificationService.info('System will undergo maintenance at midnight.');
   */
  info(content: NzNotificationContentType, title = 'Info') {
    this.createNotification(EnumNotificationMessageTypes.info, content, title);
  }

  /**
   * Display a blank/default notification
   * Example: this.notificationService.blank('This is a generic notification.');
   */
  blank(content: NzNotificationContentType, title = 'Notification') {
    this.createNotification(EnumNotificationMessageTypes.blank, content, title);
  }

  /*********************  Messages ****************************/

  /**
   * Display a success toast message
   * Example: this.notificationService.successMessage('Data saved successfully!');
   */
  successMessage(content: NzMessageContentType) {
    this.createMessage(EnumNotificationMessageTypes.success, content);
  }

  /**
   * Display an error toast message
   * Example: this.notificationService.errorMessage('Unable to complete the request.');
   */
  errorMessage(content: NzMessageContentType) {
    this.createMessage(EnumNotificationMessageTypes.error, content);
  }

  /**
   * Display an informational toast message
   * Example: this.notificationService.infoMessage('New update available.');
   */
  infoMessage(content: NzMessageContentType) {
    this.createMessage(EnumNotificationMessageTypes.info, content);
  }

  /**
   * Display a warning toast message
   * Example: this.notificationService.warningMessage('You are about to overwrite existing data.');
   */
  warningMessage(content: NzMessageContentType) {
    this.createMessage(EnumNotificationMessageTypes.warning, content);
  }

  /**
   * Display a blank toast message
   * Example: this.notificationService.blankMessage('Just letting you know.');
   */
  blankMessage(content: NzMessageContentType) {
    this.createMessage(EnumNotificationMessageTypes.blank, content);
  }

  /**
   * Display a loading message and return its messageId
   * Example:
   * // To show loader message
   * const messageId = this.notificationService.showLoaderMessage('Loading...');
   */
  showLoaderMessage(content: NzMessageContentType): string {
    return this.messageService.loading(content).messageId;
  }

  /**
   * Hide a loading message by its messageId
   * Example:
   * // To hide loader message
   * this.notificationService.hideLoaderMessage(messageId);
   */
  hideLoaderMessage(messageId: string) {
    this.messageService.remove(messageId);
  }
}

/***********************************/
/*         Usage Examples          */
/***********************************/

/**
 * 1. Display a Success Notification
 * this.notificationService.success('Operation completed successfully!');
 *
 * 2. Display an Error Notification
 * this.notificationService.error('Something went wrong. Please try again.');
 *
 * 3. Display a Warning Notification
 * this.notificationService.warning('This is a warning message.');
 *
 * 4. Display an Info Notification
 * this.notificationService.info('This is an informational message.');
 *
 * 5. Display a Blank Notification
 * this.notificationService.blank('This is a generic notification.');
 *
 * 6. Display a Success Message
 * this.notificationService.successMessage('Operation completed successfully!');
 *
 * 7. Display an Error Message
 * this.notificationService.errorMessage('Something went wrong. Please try again.');
 *
 * 8. Display a Warning Message
 * this.notificationService.warningMessage('This is a warning message.');
 *
 * 9. Display an Info Message
 * this.notificationService.infoMessage('This is an informational message.');
 *
 * 10. Display a Blank Message
 * this.notificationService.blankMessage('This is a generic message.');
 *
 * 11. Display a Loader Message
 * // To show loader message
 * const messageId = this.notificationService.showLoaderMessage('Loading...');
 *
 * 12. Hide a loader Message
 * // To hide loader message
 * this.notificationService.hideLoaderMessage(messageId);
 */
