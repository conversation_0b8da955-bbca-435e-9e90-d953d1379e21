import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import {
  ApiResponseUserResponseDTO,
  UserControllerService,
  UserRequestDTO,
  UserResponseDTO,
} from '../../api-client';
import { EnumUserRole } from '../enumerations/user-roles';
import { ROUTES } from '../constants/routes';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private _userInfo?: UserResponseDTO;
  private userControllerService = inject(UserControllerService);

  async getUserRole(): Promise<UserResponseDTO | undefined> {
    if (!this._userInfo) {
      try {
        const response = await this.userControllerService
          .getCurrentUser()
          .toPromise();
        this._userInfo = response?.data;
      } catch (error) {
        console.error('Error fetching user info', error);
        return undefined;
      }
    }

    return this._userInfo;
  }

  getPrimaryNativeRouteBasedOnUserRole(roleId: EnumUserRole) {
    switch (roleId) {
      case EnumUserRole.PLATFORM_ADMIN:
        return ROUTES.sidebar.company;
      case EnumUserRole.COMPANY_ADMIN:
      case EnumUserRole.COMPANY_MEMBER:
      case EnumUserRole.INDEPENDEDNT_AGENT:
        return ROUTES.sidebar.homepage;
      default:
        return ROUTES.auth.login;
    }
  }

  get userInfo() {
    return this._userInfo;
  }

  set userInfo(userInfo: UserResponseDTO | undefined) {
    this._userInfo = userInfo;
  }

  removeUserInfo() {
    this._userInfo = undefined;
  }
  updateUser(userId: number, userRequestDTO: UserRequestDTO) {
    return this.userControllerService.updateUser(userId, userRequestDTO);
  }

  public changePassword(email: string): Observable<ApiResponseUserResponseDTO> {
    return this.userControllerService.resetPassword(email);
  }
}
