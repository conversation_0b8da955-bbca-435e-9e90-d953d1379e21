import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { PresignedUrlResponse, S3ControllerService } from '../../api-client';
@Injectable({
  providedIn: 'root',
})
export class S3Service {
  constructor(private s3ControllerService: S3ControllerService) {}

  public getPresignedUrl(
    userEmail: string,
    fileName: string,
  ): Observable<PresignedUrlResponse> {
    return this.s3ControllerService.getPresignedUrl(userEmail, fileName);
  }
}
