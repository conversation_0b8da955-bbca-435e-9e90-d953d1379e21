import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class IndexedDBService {
  private dbName = 'ImageDB';
  private storeName = 'Images';

  constructor() {
    this.initDB();
  }

  /**
   * Initialize IndexedDB
   */
  private initDB(): void {
    const request: IDBOpenDBRequest = indexedDB.open(this.dbName, 1);

    request.onupgradeneeded = (event: IDBVersionChangeEvent) => {
      const db: IDBDatabase = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains(this.storeName)) {
        db.createObjectStore(this.storeName, {
          keyPath: 'id',
          autoIncrement: true,
        });
      }
    };

    request.onerror = () => {
      console.error('Error initializing IndexedDB');
    };
  }

  saveImage(file: File): Promise<number> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        const fileData = event.target?.result;

        const request: IDBOpenDBRequest = indexedDB.open(this.dbName);

        request.onsuccess = (event: Event) => {
          const db: IDBDatabase = (event.target as IDBOpenDBRequest).result;
          const transaction: IDBTransaction = db.transaction(
            this.storeName,
            'readwrite',
          );
          const store: IDBObjectStore = transaction.objectStore(this.storeName);

          const imageData = {
            name: file.name,
            type: file.type,
            size: file.size,
            data: fileData,
          };

          const addRequest: IDBRequest = store.add(imageData);

          addRequest.onsuccess = () => {
            const id = addRequest.result;
            if (typeof id === 'number' && id > 0) {
              resolve(id);
            } else {
              console.error('Invalid ID generated by IndexedDB:', id);
              reject('Invalid ID generated by IndexedDB');
            }
          };

          addRequest.onerror = () => {
            console.error('Error saving image to IndexedDB:', addRequest.error);
            reject('Error saving image to IndexedDB');
          };
        };

        request.onerror = () => {
          console.error('Error opening IndexedDB:', request.error);
          reject('Error opening IndexedDB');
        };
      };

      reader.onerror = () => {
        console.error('Error reading file:', reader.error);
        reject('Error reading file');
      };

      reader.readAsDataURL(file);
    });
  }

  /**
   * Retrieve an image from IndexedDB
   * @param id ID of the image to retrieve
   * @returns Promise<{ name: string; type: string; base64: string } | null>
   */
  getImage(
    id: number,
  ): Promise<{ name: string; type: string; base64: string } | null> {
    return new Promise((resolve, reject) => {
      if (typeof id !== 'number' || id <= 0 || !Number.isInteger(id)) {
        console.error('Invalid ID passed to getImage:', id);
        reject('Invalid ID passed to getImage');
        return;
      }

      const request: IDBOpenDBRequest = indexedDB.open(this.dbName);

      request.onsuccess = (event: Event) => {
        const db: IDBDatabase = (event.target as IDBOpenDBRequest).result;
        const transaction: IDBTransaction = db.transaction(
          this.storeName,
          'readonly',
        );
        const store: IDBObjectStore = transaction.objectStore(this.storeName);

        const getRequest: IDBRequest = store.get(id);

        getRequest.onsuccess = () => {
          const result = getRequest.result;
          if (result) {
            resolve({
              name: result.name,
              type: result.type,
              base64: result.data, // Base64 string
            });
          } else {
            console.error('No record found for ID:', id);
            resolve(null);
          }
        };

        getRequest.onerror = () => {
          console.error(
            'Error retrieving image from IndexedDB:',
            getRequest.error,
          );
          reject('Error retrieving image from IndexedDB');
        };
      };

      request.onerror = () => {
        console.error('Error opening IndexedDB:', request.error);
        reject('Error opening IndexedDB');
      };
    });
  }
}
