import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { ISearchData } from '../interface/search-base.interface';
import {
  ApiResponseCustomParcelResponse,
  ApiResponseCustomProductResponse,
  ApiResponsePropertyResponse,
  ApiResponseProprietorsResponse,
  ProductDetails,
  PropertyDTO,
  ProprietorSummary,
  ServApiControllerService,
} from '../../api-client';
import { IPropertySearch } from '../interface/property-search.interface';
import { IParcelProperty } from '../interface/parcel-property.interface';
import { IProprietorSummary } from '../interface/proprietor-summary.interface';

@Injectable({
  providedIn: 'root',
})
export class SearchService {
  constructor(private servApiControllerService: ServApiControllerService) {}

  public searchAddress(params: {
    propertyPfi?: string;
    eziAddress?: string;
    municipalityName?: string;
    propertyNumber?: string;
  }): Observable<IPropertySearch[]> {
    return this.servApiControllerService
      .getProperties(
        params.propertyPfi,
        params.propertyNumber,
        params.municipalityName,
        params.eziAddress,
      )
      .pipe(
        map((response: ApiResponsePropertyResponse) => {
          const propertySummaries = response.data?.propertySummaries;

          if (propertySummaries) {
            const results: IPropertySearch[] = propertySummaries.map(
              (property: PropertyDTO) => ({
                ...property,
                addressOptions: {
                  key: property.propertyPfi,
                  value: property.propertyPfi,
                  label: `${property.primaryAddress?.addressDetails?.levelNumber} ${property.primaryAddress?.eziAddress} (${property.propertyPfi})`,
                },
              }),
            );

            return results;
          }

          return [];
        }),
      );
  }

  public searchPlan(parcelIdentifier: string): Observable<IParcelProperty[]> {
    return this.servApiControllerService.getParcel(parcelIdentifier).pipe(
      map((response: ApiResponseCustomParcelResponse) => {
        const parcelResponse = response.data;

        if (parcelResponse?.properties) {
          const results: IParcelProperty[] = parcelResponse.properties.map(
            (property: PropertyDTO) => ({
              ...property,
              propertyPfiOptions: {
                key: property.propertyPfi,
                value: property.propertyPfi,
                label: property.propertyPfi + '',
              },
            }),
          );

          return results;
        }

        return [];
      }),
    );
  }

  public getProprietors(lastName: string): Observable<IProprietorSummary[]> {
    return this.servApiControllerService.getProprietors(lastName).pipe(
      map((response: ApiResponseProprietorsResponse) => {
        const proprietorSummaries = response.data?.proprietorSummaries;

        if (proprietorSummaries) {
          const results: IProprietorSummary[] = proprietorSummaries.map(
            (proprietor: ProprietorSummary) => ({
              ...proprietor,
              titleIdOptions: {
                key: proprietor.titleDetails?.titleId,
                value: proprietor.titleDetails?.titleId,
                label: proprietor.titleDetails?.landDescriptionText ?? '',
              },
            }),
          );

          return results;
        }

        return [];
      }),
    );
  }

  public getProducts(params: {
    titleId?: string;
    propertyPfi?: string;
    documentId?: string;
  }): Observable<ISearchData[]> {
    return this.servApiControllerService
      .getProducts(params.titleId, params.propertyPfi, params.documentId)
      .pipe(
        map((response: ApiResponseCustomProductResponse) => {
          const products = response.data?.products;

          if (products) {
            const results = products.map((product: ProductDetails) => ({
              ...product,
              deliveryTime: product.turnaroundTime
                ? `${product.turnaroundTime} ${products?.[0]?.turnaroundTimeUnit?.toLowerCase() || ''}${product.turnaroundTime > 1 ? '(s)' : 's'}`
                : 'N/A',
            }));

            return results;
          }

          return [];
        }),
      );
  }
}
