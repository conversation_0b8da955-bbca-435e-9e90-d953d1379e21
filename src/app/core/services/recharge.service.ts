import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import {
  WalletControllerService,
  WalletRechargeRequestDTO,
  WalletRechargeResponseDTO,
  AccountDTO,
} from '../../api-client';
import { DEFAULT_CURRENCY } from '../constants/common';

@Injectable({
  providedIn: 'root',
})
export class RechargeService {
  constructor(private walletControllerService: WalletControllerService) {}

  rechargeWallet(
    userId: number,
    amount: number,
  ): Observable<WalletRechargeResponseDTO> {
    const walletRechargeRequest: WalletRechargeRequestDTO = {
      amount,
      currency: DEFAULT_CURRENCY,
    };

    return this.walletControllerService.rechargeWallet(
      userId,
      walletRechargeRequest,
    );
  }

  getUserWalletBalance(userId: number): Observable<number> {
    return this.walletControllerService
      .getUserAccount(userId)
      .pipe(map((account: AccountDTO) => account.balance ?? 0));
  }
}
