import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { IUser } from '../interface/user.model';
import { IApiResponse } from '../interface/api-response.model';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private currentUser: IUser | null = null;
  private readonly storageKey = 'currentUser';

  constructor(
    private http: HttpClient,
    private storageService: StorageService,
  ) {
    this.loadUserFromStorage();
  }

  getCurrentUser(): IUser | null {
    return this.currentUser;
  }

  setCurrentUser(user: IUser | null): void {
    this.currentUser = user;
    if (user) {
      this.storageService.setItem<IUser>(this.storageKey, user);
    } else {
      this.storageService.removeItem(this.storageKey);
    }
  }

  login(username: string, password: string): Observable<IApiResponse<IUser>> {
    return this.http
      .post<
        IApiResponse<IUser>
      >('http://localhost:3000/api/auth/login', { username, password })
      .pipe(
        tap((response) => {
          if (response.success && response.data) {
            this.setCurrentUser(response.data);
          }
        }),
      );
  }

  logout(): void {
    this.setCurrentUser(null);
  }

  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  private loadUserFromStorage(): void {
    this.currentUser = this.storageService.getItem<IUser>(this.storageKey);
  }
}
