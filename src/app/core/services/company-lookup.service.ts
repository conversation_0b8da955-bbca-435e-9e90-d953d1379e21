import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  ApiResponseListStateDTO,
  ApiResponseListZipCodeDTO,
  LookUpControllerService,
} from '../../api-client';

@Injectable({
  providedIn: 'root',
})
export class CompanyLookupService {
  constructor(private lookUpControllerService: LookUpControllerService) {}

  public fetchZipcodes(stateId: number): Observable<ApiResponseListZipCodeDTO> {
    return this.lookUpControllerService.getZipCodes(stateId);
  }

  public fetchStates(): Observable<ApiResponseListStateDTO> {
    return this.lookUpControllerService.getStates();
  }
}
