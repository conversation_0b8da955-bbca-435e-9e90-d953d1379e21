import { TestBed } from '@angular/core/testing';
import { NotificationService } from './notification.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { EnumNotificationMessageTypes } from '../enumerations/notificationMessage';
import { Subject } from 'rxjs';

describe('NotificationService', () => {
  let service: NotificationService;
  let notificationServiceSpy: jasmine.SpyObj<NzNotificationService>;
  let messageServiceSpy: jasmine.SpyObj<NzMessageService>;

  beforeEach(() => {
    const notificationSpy = jasmine.createSpyObj('NzNotificationService', [
      'create',
    ]);
    const messageSpy = jasmine.createSpyObj('NzMessageService', [
      'create',
      'loading',
      'remove',
    ]);

    TestBed.configureTestingModule({
      providers: [
        NotificationService,
        { provide: NzNotificationService, useValue: notificationSpy },
        { provide: NzMessageService, useValue: messageSpy },
      ],
    });

    service = TestBed.inject(NotificationService);
    notificationServiceSpy = TestBed.inject(
      NzNotificationService,
    ) as jasmine.SpyObj<NzNotificationService>;
    messageServiceSpy = TestBed.inject(
      NzMessageService,
    ) as jasmine.SpyObj<NzMessageService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Notification Methods', () => {
    it('should call notificationService.create with success type', () => {
      service.success('Success Message');
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.success,
        'Success',
        'Success Message',
      );
    });

    it('should call notificationService.create with error type', () => {
      service.error('Error Message');
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.error,
        'Error',
        'Error Message',
      );
    });

    it('should call notificationService.create with warning type', () => {
      service.warning('Warning Message');
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.warning,
        'Warning',
        'Warning Message',
      );
    });

    it('should call notificationService.create with info type', () => {
      service.info('Info Message');
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.info,
        'Info',
        'Info Message',
      );
    });

    it('should call notificationService.create with blank type', () => {
      service.blank('Generic Notification');
      expect(notificationServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.blank,
        'Notification',
        'Generic Notification',
      );
    });
  });

  describe('Message Methods', () => {
    it('should call messageService.create with success type', () => {
      service.successMessage('Success Message');
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.success,
        'Success Message',
      );
    });

    it('should call messageService.create with error type', () => {
      service.errorMessage('Error Message');
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.error,
        'Error Message',
      );
    });

    it('should call messageService.create with warning type', () => {
      service.warningMessage('Warning Message');
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.warning,
        'Warning Message',
      );
    });

    it('should call messageService.create with info type', () => {
      service.infoMessage('Info Message');
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.info,
        'Info Message',
      );
    });

    it('should call messageService.create with blank type', () => {
      service.blankMessage('Blank Message');
      expect(messageServiceSpy.create).toHaveBeenCalledWith(
        EnumNotificationMessageTypes.blank,
        'Blank Message',
      );
    });
  });

  describe('Loader Message', () => {
    it('should show loader message and return messageId', () => {
      const dummyMessageId = 'loader123';
      const mockMessageRef = {
        messageId: dummyMessageId,
        onClose: new Subject<boolean>(),
      };

      messageServiceSpy.loading.and.returnValue(mockMessageRef);

      const returnedMessageId = service.showLoaderMessage('Loading...');
      expect(messageServiceSpy.loading).toHaveBeenCalledWith('Loading...');
      expect(returnedMessageId).toBe(dummyMessageId);
    });

    it('should remove loader message by messageId', () => {
      const messageId = 'loader123';
      service.hideLoaderMessage(messageId);
      expect(messageServiceSpy.remove).toHaveBeenCalledWith(messageId);
    });
  });
});
