import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class StorageService {
  setItem<T>(key: string, value: T, useSession = false): void {
    const storage = useSession ? sessionStorage : localStorage;
    storage.setItem(key, JSON.stringify(value));
  }

  getItem<T>(key: string, useSession = false): T | null {
    const storage = useSession ? sessionStorage : localStorage;
    const item = storage.getItem(key);
    try {
      return item ? (JSON.parse(item) as T) : null;
    } catch {
      // Fallback: return raw string if not valid JSON
      return item as unknown as T;
    }
  }

  removeItem(key: string, useSession = false): void {
    const storage = useSession ? sessionStorage : localStorage;
    storage.removeItem(key);
  }

  clear(useSession = false): void {
    const storage = useSession ? sessionStorage : localStorage;
    storage.clear();
  }
}
