import { EnumUserRole } from '../enumerations/user-roles';
import { ISidebarItem } from '../interface/sidebar';
import { ROUTES } from './routes';

export const SIDEBAR_MENU_ITEMS: ISidebarItem[] = [
  // Platform Admin
  // commenting for now
  // {
  //   label: 'Dashboard View',
  //   icon: 'dashboard',
  //   route: ROUTES.sidebar.dashboard,
  //   hasArrow: true,
  //   roleIds: [EnumUserRole.PLATFORM_ADMIN],
  // },
  {
    label: 'Pricing Management',
    icon: 'tag',
    route: ROUTES.sidebar.pricing,
    roleIds: [EnumUserRole.PLATFORM_ADMIN],
  },
  {
    label: 'Company Management',
    icon: 'tag',
    route: ROUTES.sidebar.company,
    roleIds: [EnumUserRole.PLATFORM_ADMIN],
  },

  {
    label: 'Search',
    icon: 'search',
    route: ROUTES.sidebar.homepage,
    hasArrow: true,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },

  {
    label: 'My Purchases',
    icon: 'shopping-cart',
    route: ROUTES.sidebar.purchases,
    hasArrow: true,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },
  {
    label: 'My Favorites',
    icon: 'star',
    route: ROUTES.sidebar.favorites,
    hasArrow: true,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },
  {
    label: 'Notifications',
    icon: 'bell',
    route: ROUTES.sidebar.notifications,
    hasArrow: true,
    roleIds: Object.values(EnumUserRole).filter(
      (role) => role != EnumUserRole.PLATFORM_ADMIN,
    ) as EnumUserRole[],
  },
];
