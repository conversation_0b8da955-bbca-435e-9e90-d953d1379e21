import moment from 'moment';
import {
  TDateSortTypes,
  TNumericSortTypes,
  TStringSortTypes,
} from '../types/sort.type';

// For case-sensitive string sorting
export const STRING_CASE_SENSITIVE_SORT_FN = (
  a: TStringSortTypes,
  b: TStringSortTypes,
): number => String(a ?? '').localeCompare(String(b ?? ''));

// For case-insensitive string sorting
export const STRING_CASE_INSENSITIVE_SORT_FN = (
  a: TStringSortTypes,
  b: TStringSortTypes,
): number =>
  String(a ?? '')
    .toLowerCase()
    .localeCompare(String(b ?? '').toLowerCase());

// For numeric sorting
export const NUM_SORT_FN = (
  a: TNumericSortTypes,
  b: TNumericSortTypes,
): number => {
  // Check if a or b is null or undefined, and return appropriate values
  if (a == null && b == null) return 0;
  if (a == null) return -1;
  if (b == null) return 1;

  // Check if a or b is NaN and handle accordingly
  if (isNaN(Number(a))) return 1; // Treat NaN as greater than other values
  if (isNaN(Number(b))) return -1; // Treat NaN as smaller than other values

  // Perform numeric sorting
  return Number(a) - Number(b);
};

// For date sorting
export const DATE_SORT_FN = (a: TDateSortTypes, b: TDateSortTypes): number => {
  // Handle null or undefined values
  if (a == null && b == null) return 0; // Both are null or undefined
  if (a == null) return -1; // Null or undefined dates come first
  if (b == null) return 1; // Non-null dates come after null or undefined

  // Parse the dates
  const dateA = moment(a);
  const dateB = moment(b);

  // Check if both dates are valid
  if (!dateA.isValid() && !dateB.isValid()) return 0; // Both invalid
  if (!dateA.isValid()) return -1; // Invalid dates come first
  if (!dateB.isValid()) return 1; // Valid dates come after invalid ones

  // Compare valid dates
  return dateA.diff(dateB); // Returns a positive/negative/zero value for sorting
};
