// import { TestBed } from '@angular/core/testing';
// import {
//   HttpRequest,
//   HttpHandler,
//   HttpEvent,
//   HttpErrorResponse
// } from '@angular/common/http';
// import { of, throwError } from 'rxjs';
// import { AuthInterceptor } from './auth.interceptor';
// import { AuthService } from '../services/auth.service';
// import { Router } from '@angular/router';
// import { StorageService } from '../services/storage.service';

// describe('AuthInterceptor', () => {
//   let interceptor: AuthInterceptor;
//   let authService: jasmine.SpyObj<AuthService>;
//   let router: jasmine.SpyObj<Router>;
//   let storageService: jasmine.SpyObj<StorageService>;

//   beforeEach(() => {
//     const authServiceSpy = jasmine.createSpyObj('AuthService', ['setCurrentUser']);
//     const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
//     const storageSpy = jasmine.createSpyObj('StorageService', ['getItem']);

//     TestBed.configureTestingModule({
//       providers: [
//         AuthInterceptor,
//         { provide: AuthService, useValue: authServiceSpy },
//         { provide: Router, useValue: routerSpy },
//         { provide: StorageService, useValue: storageSpy }
//       ]
//     });

//     interceptor = TestBed.inject(AuthInterceptor);
//     authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
//     router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
//     storageService = TestBed.inject(StorageService) as jasmine.SpyObj<StorageService>;
//   });

//   it('should be created', () => {
//     expect(interceptor).toBeTruthy();
//   });

//   it('should add authorization header when token exists', () => {
//     storageService.getItem.and.returnValue('test-token');
//     const request = new HttpRequest('GET', '/test');
//     const next: HttpHandler = {
//       handle: (req: HttpRequest<unknown>) => {
//         expect(req.headers.get('Authorization')).toBe('Bearer test-token');
//         return of({} as HttpEvent<unknown>);
//       }
//     };

//     interceptor.intercept(request, next).subscribe();

//     expect(storageService.getItem).toHaveBeenCalledWith('token');
//   });

//   it('should not add authorization header when token does not exist', () => {
//     storageService.getItem.and.returnValue(null);
//     const request = new HttpRequest('GET', '/test');
//     const next: HttpHandler = {
//       handle: (req: HttpRequest<unknown>) => {
//         expect(req.headers.get('Authorization')).toBeNull();
//         return of({} as HttpEvent<unknown>);
//       }
//     };

//     interceptor.intercept(request, next).subscribe();

//     expect(storageService.getItem).toHaveBeenCalledWith('token');
//   });

//   it('should handle 401 error and redirect to login', () => {
//     const request = new HttpRequest('GET', '/test');
//     const error = new HttpErrorResponse({ status: 401 });
//     const next: HttpHandler = {
//       handle: () => throwError(() => error)
//     };

//     interceptor.intercept(request, next).subscribe({
//       error: () => {
//         expect(authService.setCurrentUser).toHaveBeenCalledWith(null);
//         expect(router.navigate).toHaveBeenCalledWith(['/login']);
//       }
//     });
//   });
// });
