import {
  HttpRequest,
  HttpEvent,
  HttpInterceptorFn,
  HttpHandlerFn,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { StorageService } from '../services/storage.service';
import { StorageKeys } from '../enumerations/storage-keys.enum';
import { PUBLIC_ENDPOINTS } from '../constants/app-routes.constants';
import { HttpStatusCode } from '../enumerations/http-status-code.enum';
import { ROUTES } from '../constants/routes';
import { mediaURL } from '../constants/common';

export const authInterceptor: HttpInterceptorFn = (
  request: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> => {
  const storage = inject(StorageService);
  const router = inject(Router);

  const token = storage.getItem<string>(StorageKeys.ACCESS_TOKEN);
  const isPublicUrl = PUBLIC_ENDPOINTS.some((endpoint) =>
    request.url.includes(endpoint),
  );
  const isS3Request = request.url.includes(mediaURL);

  // Skip adding Authorization header for public URLs and S3 requests
  if (token && !isPublicUrl && !isS3Request && token) {
    request = request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  return next(request).pipe(
    catchError((error: HttpErrorResponse) => {
      if (error.status === HttpStatusCode.UNAUTHORIZED) {
        storage.removeItem(StorageKeys.ACCESS_TOKEN);
        router.navigate([ROUTES.auth.login]);
      }
      return throwError(() => error);
    }),
  );
};
