export enum EnumSearchType {
  Property = 'Property Search',
  Address = 'Address Search',
  Plan = 'Plan Search',
  Proprietor = 'Proprietorship Search',
  Map = 'Map Search',
  Title = 'Title Search',
  Document = 'Document Search',
  Alerts = 'Alerts',
}

export enum EnumSearchTypeHeader {
  Property = 'Property Search',
  Address = 'Address Search',
  Plan = 'Lot-Plan Search',
  Proprietor = 'Proprietorship Search',
  Map = 'Map Search',
  Title = 'Title Search',
  Document = 'Document Search',
  Alerts = 'Title Alerts Search',
}
export enum EnumSearchTabIndex {
  Property = 0,
  Document = 1,
  Alerts = 2,
}
