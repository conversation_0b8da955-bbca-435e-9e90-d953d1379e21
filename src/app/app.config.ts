import { ApplicationConfig } from '@angular/core';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideRouter } from '@angular/router';
import { NZ_ICONS } from 'ng-zorro-antd/icon';
import {
  UserOutline,
  LoginOutline,
  ApartmentOutline,
} from '@ant-design/icons-angular/icons';
import { routes } from './app.routes';
import { provideNzI18n, en_US } from 'ng-zorro-antd/i18n'; // Import the English locale
import { IconDefinition } from '@ant-design/icons-angular';
import { provideNzIcons } from 'ng-zorro-antd/icon';
import * as AllIcons from '@ant-design/icons-angular/icons';
import { authInterceptor } from './core/interceptors/auth.interceptor';

const antDesignIcons = AllIcons as Record<string, IconDefinition>;
const icons: IconDefinition[] = Object.keys(antDesignIcons).map(
  (key) => antDesignIcons[key],
);
export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(withInterceptors([authInterceptor])),
    {
      provide: NZ_ICONS,
      useValue: [UserOutline, LoginOutline, ApartmentOutline],
    },
    provideNzI18n(en_US),
    provideNzIcons(icons),
  ],
};
