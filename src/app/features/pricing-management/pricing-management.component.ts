import { Component, OnInit } from '@angular/core';
import { DataGridComponent } from '../data-grid/data-grid.component';
import {
  DUMMY_PRICING_DATA,
  PRICING_MANAGEMENT_GLOBAL_LEVEL_TABLE_COLUMNS,
} from '../../core/tableColumns/pricing.column';
import { IPricingFields } from '../../core/interface/pricing-fields';
import { ITableDataClickOutput } from '../../core/interface/table';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { CommonModule } from '@angular/common';
import { COMMON_STRINGS } from '../../core/constants/common';
import { NotificationService } from '../../core/services/notification.service';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

@Component({
  selector: 'app-pricing-management',
  imports: [
    DataGridComponent,
    NzInputModule,
    NzIconModule,
    FormsModule,
    NzButtonModule,
    NzDrawerModule,
    NzFormModule,
    CommonModule,
    ReactiveFormsModule,
    NzDatePickerModule,
  ],
  templateUrl: './pricing-management.component.html',
  styleUrls: ['./pricing-management.component.scss', '../../../styles.scss'],
})
export class PricingManagementComponent implements OnInit {
  pricingManagementTableColumns = PRICING_MANAGEMENT_GLOBAL_LEVEL_TABLE_COLUMNS;
  pricingManagementTableData = DUMMY_PRICING_DATA;
  filteredTableData: IPricingFields[] = [];
  isLoading = false;
  searchText = '';
  isAddDocumentDrawerOpen = false;
  addDocumentForm: FormGroup;
  isEditMode = false;
  selectedDocumentToEdit: IPricingFields | null = null;
  originalFormValues: IPricingFields | null = null;

  constructor(
    private fb: FormBuilder,
    private notification: NotificationService,
  ) {
    this.addDocumentForm = this.fb.group({
      documentName: [''],
      productCode: [''],
      description: [''],
      productPriceExGst: [''],
      MarkedUpPrice: [''],
      gst: [''],
      productPriceIncGst: [''],
    });
  }

  ngOnInit(): void {
    this.filteredTableData = [...this.pricingManagementTableData];
    this.addDocumentForm.valueChanges.subscribe(() => {
      this.checkFormChanges();
    });
  }

  onTableDataClick(data: ITableDataClickOutput<IPricingFields>): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'edit':
        this.onEditClick(rowData);
        break;
      case 'delete':
        this.onClickDelete(rowData);
        break;
    }
  }

  onSearch() {
    const search = this.searchText.trim().toLowerCase();
    this.filteredTableData = this.pricingManagementTableData.filter(
      (item) =>
        item.documentName.toLowerCase().includes(search) ||
        item.productCode.toLowerCase().includes(search),
    );
  }

  onEditClick(rowData: IPricingFields) {
    this.isEditMode = true;
    this.selectedDocumentToEdit = rowData;
    this.addDocumentForm.patchValue(rowData);
    this.originalFormValues = { ...rowData };
    this.toggleAddDocumentDrawer();
  }

  onClickDelete(rowData: IPricingFields) {
    console.log('delete', rowData);
  }

  toggleAddDocumentDrawer(): void {
    this.isAddDocumentDrawerOpen = !this.isAddDocumentDrawerOpen;
    if (this.isAddDocumentDrawerOpen) {
      this.setFormValidators();
    } else {
      this.addDocumentForm.reset();
      this.clearFormValidators();
      this.isEditMode = false;
      this.selectedDocumentToEdit = null;
      this.originalFormValues = null;
    }
  }

  setFormValidators(): void {
    const requiredFields = [
      'documentName',
      'description',
      'productPriceExGst',
      'MarkedUpPrice',
      'gst',
      'productPriceIncGst',
    ];

    requiredFields.forEach((field) => {
      const control = this.addDocumentForm.get(field);
      if (!this.isEditMode) {
        control?.setValidators(Validators.required);
      } else {
        control?.setValidators([Validators.required]);
      }
      control?.updateValueAndValidity();
    });
  }

  clearFormValidators(): void {
    Object.keys(this.addDocumentForm.controls).forEach((key) => {
      const control = this.addDocumentForm.get(key);
      control?.setValidators([]);
      control?.updateValueAndValidity();
    });
  }

  getErrorTip(controlName: string): string | undefined {
    const control = this.addDocumentForm.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    return undefined;
  }

  isAddedDocumentFormValid(): boolean {
    const documentForm = this.addDocumentForm;
    const documentName = this.addDocumentForm.get('documentName')?.value;

    if (this.isEditMode) {
      return documentForm.valid;
    } else {
      return !!(
        documentForm.valid &&
        (!documentName || this.isDocumentNameUnique(documentName))
      );
    }
  }

  isDocumentNameUnique(documentName: string): boolean {
    if (this.isEditMode && this.selectedDocumentToEdit) {
      return !this.pricingManagementTableData.some(
        (item) =>
          item.documentName?.toLowerCase() === documentName.toLowerCase() &&
          item !== this.selectedDocumentToEdit,
      );
    }
    return !this.pricingManagementTableData.some(
      (item) => item.documentName?.toLowerCase() === documentName.toLowerCase(),
    );
  }

  private hasFormChanged(): boolean {
    if (!this.originalFormValues) return false;

    const currentValues = this.addDocumentForm.getRawValue();

    const allKeys = new Set([
      ...Object.keys(currentValues),
      ...Object.keys(this.originalFormValues),
    ]);

    return Array.from(allKeys).some((key) => {
      const current = currentValues[key as keyof IPricingFields];
      const original = this.originalFormValues![key as keyof IPricingFields];
      return current !== original;
    });
  }

  private checkFormChanges(): void {
    if (this.isEditMode && !this.hasFormChanged()) {
      this.addDocumentForm.markAsPristine();
    }
  }

  addDocument() {
    console.log('add');
  }

  updateDocument() {
    if (this.isEditMode) {
      if (!this.hasFormChanged()) {
        this.notification.warning(
          COMMON_STRINGS.warningMessages.noChangesFound,
        );
        return;
      }

      if (!this.addDocumentForm.valid) {
        this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
        return;
      }
      this.toggleAddDocumentDrawer();
    }
  }

  onChange(date: Date) {
    if (date) {
      this.addDocumentForm.get('effectiveFrom')?.setValue(date);
    }
  }
}
