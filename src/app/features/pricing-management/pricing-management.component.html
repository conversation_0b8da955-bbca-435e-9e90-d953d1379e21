<div class="pricing-management-container">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
    <div class="search-container">
      <nz-input-group [nzSuffix]="suffixIconSearch">
        <input class="search-input" type="text" nz-input placeholder="Search document here" [(ngModel)]="searchText"
          (ngModelChange)="onSearch()" />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <nz-icon nzType="search" style="color: #666; font-size: 16px;" />
      </ng-template>
    </div>
    <button class="add-document-button" nz-button nzType="primary" (click)="toggleAddDocumentDrawer()">
      Add document
    </button>
  </div>

  <app-data-grid [tableColumns]="pricingManagementTableColumns" [tableData]="filteredTableData" [loading]="isLoading"
    [showPagination]="true" (tableDataClick)="onTableDataClick($event)">
  </app-data-grid>

  <nz-drawer [nzClosable]="false" class="drawer-header" [nzVisible]="isAddDocumentDrawerOpen"
    [nzTitle]="isEditMode ? 'Edit Document' : 'Add Document'" nzPlacement="right"
    (nzOnClose)="toggleAddDocumentDrawer()">
    <ng-container *nzDrawerContent>
      <form nz-form nzLayout="vertical" [formGroup]="addDocumentForm">
        <div nz-row nzGutter="16">
          <div nz-col [nzMd]="24">
            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="!isEditMode">Document Name</nz-form-label>
              <nz-form-control [nzErrorTip]="getErrorTip('documentName')">
                <input class="form-control nz-drawer-form-field" nz-input placeholder="Enter document name"
                  formControlName="documentName" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="!isEditMode">Description</nz-form-label>
              <nz-form-control [nzErrorTip]="getErrorTip('description')">
                <input class="form-control nz-drawer-form-field" nz-input placeholder="Enter description"
                  formControlName="description" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="!isEditMode">Platform Price (Ex. GST)</nz-form-label>
              <nz-form-control [nzErrorTip]="getErrorTip('productPriceExGst')">
                <input class="form-control nz-drawer-form-field" nz-input placeholder="Enter platform price (Ex. GST)"
                  type="number" formControlName="productPriceExGst" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="!isEditMode">Updated Price</nz-form-label>
              <nz-form-control [nzErrorTip]="getErrorTip('MarkedUpPrice')">
                <input class="form-control nz-drawer-form-field" nz-input placeholder="Enter marked up price"
                  type="number" formControlName="MarkedUpPrice" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="!isEditMode">GST</nz-form-label>
              <nz-form-control [nzErrorTip]="getErrorTip('gst')">
                <input class="form-control nz-drawer-form-field" nz-input placeholder="Enter GST amount" type="number"
                  formControlName="gst" />
              </nz-form-control>
            </nz-form-item>

            <nz-form-item class="mb-3 nzdrawer-form-item">
              <nz-form-label class="form-label" [nzRequired]="!isEditMode">Effective from</nz-form-label>
              <nz-form-control [nzErrorTip]="getErrorTip('effectiveFrom')">
                <nz-date-picker class="form-control nz-drawer-form-field" 
                  formControlName="effectiveFrom"
                  nzPlaceHolder="Select effective date"
                  (ngModelChange)="onChange($event)">
                </nz-date-picker>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div class="user-edit-form-buttons mt-6 d-flex">
          <button nz-button nzType="primary" class="save-edit-button" type="button"
            (click)="isEditMode ? updateDocument() : addDocument()"
            [disabled]="!isAddedDocumentFormValid()">{{isEditMode ? 'Save Document' : 'Add Document'}}</button>
          <button nz-button nzType="primary" nzGhost (click)="toggleAddDocumentDrawer()">
              Cancel
          </button>
        </div>
      </form>
    </ng-container>
  </nz-drawer>
</div>