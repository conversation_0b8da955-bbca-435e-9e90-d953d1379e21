.container {
  width: 100%;
  margin: 0 auto;
}

.custom-wrapper {
  padding: 0 25px;
  width: 100%;
}

.wallet-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.company-info {
  h5 {
    margin-bottom: 0;
    font-weight: 600;
  }

  .subtext {
    color: #6c757d;
  }

  .wallet-label {
    margin-top: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .wallet-balance {
    margin-top: 0.5rem;
    font-size: 22px;
    font-weight: 600;
  }
}

.stats {
  text-align: right;

  .stats-row {
    display: flex;
    justify-content: flex-end;
    gap: 30px;
  }
}

::ng-deep .ant-collapse-item {
  background-color: #e9f4ff;
  border-radius: 10px;
  margin-bottom: 12px;
  border: none;
}

.page-title {
  margin-bottom: 2px;
}

.header-container {
  margin-bottom: 20px;
}

.content-area {
  padding: 0;
}

/* Transaction Card */
.transaction-card {
  border-radius: 8px;
  border: 1px solid #e7e7e7;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;
  cursor: pointer;
  background-color: var(--the-sky-blue);
  font-size: 16px;
  font-weight: 900;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }

  .card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .doc-title {
        display: flex;
        align-items: center;

        i {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      small {
        color: #666;
      }

      .price {
        font-weight: 600;
        font-size: 18px;
        color: #333;
        margin-top: 4px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;

      .expand-icon {
        color: #999;
        font-size: 16px;
        transition: transform 0.3s;
      }
    }
  }
}

.expanded-content {
  border-top: 1px solid #f0f0f0;

  .details {
    color: #666;
    font-size: 14px;
  }
}

:host ::ng-deep .ant-list-item {
  padding: 0;
}

/* Drawer */
.drawer-header {
  background: var(--primary-button-color);
  padding: 24px;
  color: white;
  border-bottom: 1px solid #e6f7ff;

  .user-info {
    display: flex;
    align-items: center;
    gap: 16px;

    .wallet-icon {
      font-size: 32px;
    }

    .user-details {
      .greeting {
        font-size: 16px;
        font-weight: 600;
      }

      .username {
        font-weight: 700;
        font-size: 18px;
      }

      .subtext {
        font-size: 12px;
        color: #e6f7ff;
      }
    }
  }
}

.drawer-body {
  padding: 24px;
  background-color: #fafafa;
}
.transaction-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px; // increased from 16px to 20px
  margin-bottom: 1rem;

  h5 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    flex-shrink: 0;
  }

  .search-group {
    display: flex;
    flex: 1;
    align-items: center;
    gap: 16px; // more space between inputs

    nz-input-group,
    .date-picker {
      flex: 1;
    }

    ::ng-deep .ant-input,
    ::ng-deep .ant-picker {
      height: 40px;

      border-radius: 6px;
      transition:
        border-color 0.3s,
        box-shadow 0.3s;
    }

    ::ng-deep .ant-input:focus,
    ::ng-deep .ant-picker-focused {
      border-color: var(--primary-button-color);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    ::ng-deep .ant-input-suffix,
    ::ng-deep .ant-picker-suffix {
      color: #1d4ed8;
    }
  }
}
