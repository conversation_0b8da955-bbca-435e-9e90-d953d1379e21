<!-- Top-level wrapper -->
<div class="custom-wrapper">
  <!-- Content area -->
  <div class="content-area">
    <!-- Wallet Info -->
    <div class="wallet-header">
      <div class="company-info">
        <div class="wallet-label">
          Current Wallet Balance <i class="fas fa-wallet"></i>
        </div>
        <div class="wallet-balance">$ {{ walletBalance.toFixed(2) }}</div>
      </div>
      <div class="stats">
        <button
          nz-button
          nzType="primary"
          class="mt-3"
          (click)="drawerVisible = true"
        >
          Recharge
        </button>
        <nz-drawer
          [nzVisible]="drawerVisible"
          nzTitle="Recharge Wallet"
          nzPlacement="right"
          [nzClosable]="true"
          (nzOnClose)="drawerVisible = false"
          [nzExtra]="extraForWallet"
          [nzWidth]="400"
          [nzBodyStyle]="{ padding: '0px' }"
        >
          <ng-container *nzDrawerContent>
            <app-wallet
              [(amount)]="amount"
              [visibleWallet]="drawerVisible"
              (visibleWalletChange)="drawerVisible = $event"
              (visibleWalletAfterDelay)="onDrawerCloseAfterDelay($event)"
            ></app-wallet>
          </ng-container>
        </nz-drawer>
      </div>
    </div>

    <hr />

    <!-- Heading and Search Side by Side -->
    <div class="transaction-header">
      <h5>Transaction</h5>

      <div class="search-group">
        <!-- User Search -->
        <div *ngIf="getCurrentUserRole() === enumUserRole.COMPANY_ADMIN">
          <nz-input-group [nzSuffix]="searchIcon">
            <input
              type="text"
              nz-input
              placeholder="Search Users"
              [(ngModel)]="searchText"
              (ngModelChange)="searchTransaction()"
            />
          </nz-input-group>
        </div>

        <!-- Transaction Search -->
        <nz-input-group [nzSuffix]="searchIcon">
          <input
            type="text"
            nz-input
            placeholder="Find Transactions"
            [(ngModel)]="searchText"
            (ngModelChange)="searchTransaction()"
          />
        </nz-input-group>

        <!-- Date Picker -->
        <nz-range-picker
          [(ngModel)]="selectedDateRange"
          (ngModelChange)="onRangeChange($event)"
          nzFormat="dd-MM-yyyy"
          nzAllowClear
          class="date-picker"
        ></nz-range-picker>
      </div>

      <ng-template #searchIcon>
        <i nz-icon nzType="search"></i>
      </ng-template>
    </div>

    <!-- Transaction List -->
    <nz-list
      [nzDataSource]="transactions"
      [nzRenderItem]="item"
      [nzBordered]="false"
      nzItemLayout="vertical"
      [class.p-0]="true"
    >
      <ng-template #item let-transactions>
        @if (isLoading) {
          <nz-skeleton
            [nzActive]="isLoading"
            [nzParagraph]="{ rows: 1, width: '90%' }"
          ></nz-skeleton>
        } @else {
          @if (!transactions.expanded) {
            <nz-card
              class="transaction-card mb-4"
              [class.expanded]="transactions.expanded"
              [nzBordered]="false"
              (click)="toggleExpand(transactions)"
            >
              <div class="card-content">
                <div class="left">
                  <div class="doc-title">
                    <span>
                      Transaction ID:
                      {{ transactions.id || transactions.transactionId }}
                    </span>
                  </div>
                  <div>
                    Date: {{ transactions.transactionDate | date: 'MMM d, y'
                    }}<br />
                    Time: {{ transactions.transactionDate | date: 'h:mm a' }}
                  </div>
                  <span>$ {{ transactions.amount }}</span>
                </div>
                <div class="right">
                  <i
                    nz-icon
                    [nzType]="transactions.expanded ? 'down' : 'right'"
                    nzTheme="outline"
                    class="expand-icon"
                  ></i>
                </div>
              </div>
            </nz-card>
          } @else {
            <div class="expanded-content">
              <div class="details">
                <app-transactions
                  [transactionData]="[transactions]"
                  [fromPurchaseComp]="true"
                  (collapseIconClick)="toggleExpand(transactions)"
                ></app-transactions>
              </div>
            </div>
          }
        }
      </ng-template>
    </nz-list>
  </div>
</div>

<ng-template #extraForWallet>
  <button nz-button nzType="primary" nzGhost (click)="closeWalletDrawer()">
    <nz-icon
      data-testid="wallet-close-drawer"
      nzType="close-circle"
      nzTheme="fill"
    />
    Close
  </button>
</ng-template>
