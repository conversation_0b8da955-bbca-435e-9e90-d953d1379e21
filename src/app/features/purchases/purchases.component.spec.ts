import { ComponentFixture, TestBed, fakeAsync } from '@angular/core/testing';
import { PurchasesComponent } from './purchases.component';
import { ActivatedRoute } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { TransactionService } from '../../core/services/transaction.service';
import { RechargeService } from '../../core/services/recharge.service';
import { NotificationService } from '../../core/services/notification.service';
import { UserService } from '../../core/services/user.service';

import { EnumTransactionTypes } from '../../core/enumerations/transaction-types';
import { COMMON_STRINGS } from '../../core/constants/common';

describe('PurchasesComponent', () => {
  let component: PurchasesComponent;
  let fixture: ComponentFixture<PurchasesComponent>;

  const mockUser = { id: 1, firstName: 'Test User' };
  const transactionsMock = [
    {
      transactionId: 'TXN123',
      transactionDate: new Date(),
      amount: 100,
      transactionType: EnumTransactionTypes.RECHARGE,
    },
    {
      transactionId: 'TXN456',
      transactionDate: new Date(),
      amount: 50,
      transactionType: EnumTransactionTypes.PURCHASE,
    },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PurchasesComponent, RouterTestingModule],
      providers: [
        {
          provide: UserService,
          useValue: {
            getUserRole: jasmine.createSpy().and.resolveTo(mockUser),
            userInfo: mockUser,
          },
        },
        {
          provide: TransactionService,
          useValue: {
            getAllTransactions: jasmine
              .createSpy()
              .and.returnValue(of(transactionsMock)),
          },
        },
        {
          provide: RechargeService,
          useValue: {
            getUserWalletBalance: jasmine.createSpy().and.returnValue(of(1000)),
            rechargeWallet: jasmine
              .createSpy()
              .and.returnValue(of({ newBalance: 1100 })),
          },
        },
        {
          provide: NotificationService,
          useValue: {
            showLoaderMessage: jasmine
              .createSpy()
              .and.returnValue('loading-id'),
            hideLoaderMessage: jasmine.createSpy(),
            error: jasmine.createSpy(),
          },
        },
        {
          provide: ActivatedRoute,
          useValue: { snapshot: { queryParams: {} } },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PurchasesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should load wallet balance on initialization', fakeAsync(async () => {
    const rechargeService = TestBed.inject(RechargeService);
    (rechargeService.getUserWalletBalance as jasmine.Spy).and.returnValue(
      of(1000),
    );

    await component.ngOnInit();
    expect(component.walletBalance).toBe(1000);
  }));

  it('should set preset amount when clicked', () => {
    component.setPresetAmount(500);
    expect(component.amount).toBe(500);
  });

  it('should return current user name', () => {
    expect(component.getCurrentUser()).toBe('Test User');
  });

  it('should show error when user not found', fakeAsync(async () => {
    const userService = TestBed.inject(UserService);
    (userService.getUserRole as jasmine.Spy).and.resolveTo(null);

    const errorComponent =
      TestBed.createComponent(PurchasesComponent).componentInstance;
    const notification = TestBed.inject(NotificationService);

    await errorComponent.ngOnInit();
    expect(notification.error).toHaveBeenCalledWith(
      COMMON_STRINGS.errorMessages.handleUserError,
    );
  }));
});
