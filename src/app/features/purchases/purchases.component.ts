import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// NG-ZORRO Modules (Grouped Separately)
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

// Feature Components
import { TransactionsComponent } from './transactions/transactions.component';
import { WalletComponent } from '../../core/features/wallet/wallet.component';

// Services
import { TransactionService } from '../../core/services/transaction.service';
import { RechargeService } from '../../core/services/recharge.service';
import { NotificationService } from '../../core/services/notification.service';
import { UserService } from '../../core/services/user.service';

// Constants
import { ROUTES } from '../../core/constants/routes';
import {
  COMMON_STRINGS,
  DEFAULT_RECHARGE_AMOUNT,
  PRESET_AMOUNT,
} from '../../core/constants/common';

// DTOs & Enums
import {
  OrderItemDTO,
  TransactionDTO as BaseTransactionDTO,
} from '../../api-client';
import { EnumTransactionTypes } from '../../core/enumerations/transaction-types';
import { EnumUserRole } from '../../core/enumerations/user-roles';

interface TransactionDTO extends BaseTransactionDTO {
  expanded?: boolean;
}

@Component({
  selector: 'app-purchases',
  standalone: true,
  templateUrl: './purchases.component.html',
  styleUrls: ['./purchases.component.scss'],
  imports: [
    CommonModule,
    FormsModule,

    // UI Modules
    NzButtonModule,
    NzCardModule,
    NzCollapseModule,
    NzDrawerModule,
    NzIconModule,
    NzInputModule,
    NzInputNumberModule,
    NzListModule,
    NzModalModule,
    NzSkeletonModule,
    NzDatePickerModule,

    // Feature Components
    TransactionsComponent,
    WalletComponent,
  ],
})
export class PurchasesComponent implements OnInit {
  @Input() transactionId?: number;

  walletBalance = 0;
  transactionData: TransactionDTO[] = [];
  allTransactions: TransactionDTO[] = [];
  originalTransactions: TransactionDTO[] = [];
  orderData: OrderItemDTO[] = [];

  isLoading = false;
  drawerVisible = false;
  rechargeModalVisible = false;

  searchText = '';
  amount: number = DEFAULT_RECHARGE_AMOUNT;
  presetAmounts = PRESET_AMOUNT;

  selectedDate: Date | null = null;
  selectedDateRange: [Date, Date] | null = null;

  readonly enumUserRole = EnumUserRole;

  private userId?: number;

  constructor(
    private transactionService: TransactionService,
    private rechargeService: RechargeService,
    private notificationService: NotificationService,
    private userService: UserService,
    private route: ActivatedRoute,
    private router: Router,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.setUser();
    this.loadWalletBalance();
    this.loadTransactions();
  }

  private async setUser(): Promise<void> {
    try {
      const userInfo = await this.userService.getUserRole();
      this.userId = userInfo?.id;
      if (!this.userId) {
        this.showError(COMMON_STRINGS.errorMessages.handleUserError);
      }
    } catch (error) {
      console.error('Error getting user:', error);
      this.showError(COMMON_STRINGS.errorMessages.handleUserError);
    }
  }

  private loadTransactions(): void {
    this.isLoading = true;
    this.transactionService.getAllTransactions().subscribe({
      next: (res) => {
        this.transactionData = res;
        this.allTransactions = res;
        this.originalTransactions = [...res];
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Transactions error:', err);
        this.showError(COMMON_STRINGS.errorMessages.failedToFetchTransactions);
      },
    });
  }

  private loadWalletBalance(): void {
    if (!this.userId) return;
    this.rechargeService.getUserWalletBalance(this.userId).subscribe({
      next: (balance) => (this.walletBalance = balance),
      error: (err) => {
        console.error('Wallet balance error:', err);
        this.showError(COMMON_STRINGS.errorMessages.failedToFetchWalletBalance);
      },
    });
  }

  rechargeWallet(): void {
    if (!this.userId) {
      this.showError(COMMON_STRINGS.errorMessages.handleUserError);
      return;
    }

    const loadingId = this.notificationService.showLoaderMessage(
      COMMON_STRINGS.loadingMessage.rechargeWallet,
    );

    this.rechargeService.rechargeWallet(this.userId, this.amount).subscribe({
      next: (res) => {
        this.notificationService.hideLoaderMessage(loadingId);
        this.walletBalance = res.newBalance ?? this.walletBalance;

        const currentRoute = this.router.url;
        this.router.navigate([ROUTES.mockRecharge], {
          queryParams: { paymentAmount: this.amount },
        });

        setTimeout(() => {
          this.router.navigate([currentRoute]);
        }, 10000);

        this.drawerVisible = false;
      },
      error: (err) => {
        console.error('Recharge error:', err);
        this.notificationService.hideLoaderMessage(loadingId);
        this.showError(COMMON_STRINGS.errorMessages.failedToRechargeWallet);
      },
    });
  }

  onDrawerCloseAfterDelay(delay: number): void {
    setTimeout(() => (this.drawerVisible = false), delay);
  }

  searchTransaction(): void {
    const searchValue = this.searchText.trim().toLowerCase();
    this.transactionData = searchValue
      ? this.originalTransactions.filter((txn) =>
          txn.transactionId?.toString().toLowerCase().includes(searchValue),
        )
      : [...this.originalTransactions];
  }

  onDateChange(date: Date | null): void {
    this.selectedDate = date;
    if (!date) {
      this.transactionData = [...this.originalTransactions];
      return;
    }

    const selected = new Date(date).setHours(0, 0, 0, 0);
    this.transactionData = this.originalTransactions.filter((txn) => {
      const txnDate = new Date(txn.transactionDate ?? 0).setHours(0, 0, 0, 0);
      return txnDate === selected;
    });
  }

  onRangeChange(dates: [Date, Date] | null): void {
    if (!dates?.length) {
      this.transactionData = [...this.originalTransactions];
      return;
    }

    const [start, end] = dates;
    const adjustedStart = new Date(start).setHours(0, 0, 0, 0);
    const adjustedEnd = new Date(end).setHours(23, 59, 59, 999);

    this.transactionData = this.originalTransactions.filter((txn) => {
      const txnDate = new Date(txn.transactionDate ?? 0).getTime();
      return txnDate >= adjustedStart && txnDate <= adjustedEnd;
    });
  }

  clearDateFilter(): void {
    this.selectedDate = null;
    this.transactionData = [...this.originalTransactions];
  }

  toggleExpand(transaction: TransactionDTO): void {
    transaction.expanded = !transaction.expanded;
  }

  setPresetAmount(value: number): void {
    this.amount = value;
  }

  closeWalletDrawer(): void {
    this.drawerVisible = false;
  }

  getCurrentUser(): string {
    return this.userService.userInfo?.firstName ?? 'John Doe';
  }

  getCurrentUserRole(): number {
    return this.userService.userInfo?.roleId ?? 0;
  }

  get transactions(): TransactionDTO[] {
    return this.isLoading
      ? Array(10).fill({} as TransactionDTO)
      : this.transactionData;
  }

  get rechargeTransactions(): TransactionDTO[] {
    return this.allTransactions.filter(
      (txn) => txn.transactionType === EnumTransactionTypes.RECHARGE,
    );
  }

  private showError(message: string): void {
    this.notificationService.error(message);
    this.isLoading = false;
  }
}
