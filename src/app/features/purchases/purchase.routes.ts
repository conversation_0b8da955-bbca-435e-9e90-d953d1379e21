import { Routes } from '@angular/router';
import { TransactionsComponent } from './transactions/transactions.component';
import { PurchasesComponent } from './purchases.component';
import { ROUTES } from '../../core/constants/routes';

export const TRANSACTIONS_ROUTES: Routes = [
  {
    path: '',
    component: PurchasesComponent,
    pathMatch: 'full',
  },
  {
    path: ROUTES.sidebar.transactions,
    children: [
      {
        path: '',
        component: TransactionsComponent,
        pathMatch: 'full',
      },
      {
        path: ':transactionId',
        component: TransactionsComponent,
      },
    ],
  },
];
