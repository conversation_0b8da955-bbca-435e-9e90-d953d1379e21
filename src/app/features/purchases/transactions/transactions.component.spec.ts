// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { HttpClientTestingModule } from '@angular/common/http/testing';
// import { TransactionsComponent } from './transactions.component';
// import { TransactionService } from '../../core/services/transaction.service';
// import { UserService } from '../../core/services/user.service';
// import { NotificationService } from '../../core/services/notification.service';
// import { of } from 'rxjs';
// import { NO_ERRORS_SCHEMA } from '@angular/core';

// describe('TransactionsComponent', () => {
//   let component: TransactionsComponent;
//   let fixture: ComponentFixture<TransactionsComponent>;
//   let transactionServiceSpy: jasmine.SpyObj<TransactionService>;
//   let userServiceSpy: jasmine.SpyObj<UserService>;
//   let notificationServiceSpy: jasmine.SpyObj<NotificationService>;

//   beforeEach(async () => {
//     // Create spies for services
//     transactionServiceSpy = jasmine.createSpyObj('TransactionService', [
//       'getAllTransactions',
//     ]);
//     userServiceSpy = jasmine.createSpyObj('UserService', ['getUserRole'], {
//       userInfo: null,
//     });
//     notificationServiceSpy = jasmine.createSpyObj('NotificationService', [
//       'error',
//       'successMessage',
//     ]);

//     // Mock the service responses
//     transactionServiceSpy.getAllTransactions.and.returnValue(of([]));

//     await TestBed.configureTestingModule({
//       imports: [TransactionsComponent, HttpClientTestingModule],
//       providers: [
//         { provide: TransactionService, useValue: transactionServiceSpy },
//         { provide: UserService, useValue: userServiceSpy },
//         { provide: NotificationService, useValue: notificationServiceSpy },
//       ],
//       schemas: [NO_ERRORS_SCHEMA], // Ignore unknown elements and attributes
//     }).compileComponents();

//     fixture = TestBed.createComponent(TransactionsComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
