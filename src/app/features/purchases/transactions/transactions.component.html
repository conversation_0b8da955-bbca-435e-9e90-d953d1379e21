@if (transactionId) {
  <nz-breadcrumb class="ms-5 mt-2">
    <nz-breadcrumb-item>
      <a (click)="onClickPurchases()">My Purchases</a>
    </nz-breadcrumb-item>
    <nz-breadcrumb-item>
      <a>Transaction Details</a>
    </nz-breadcrumb-item>
  </nz-breadcrumb>
}

<!-- 🔧 Wrapper to add left-right spacing -->
<div class="transaction-wrapper" [class.m-5]="!fromPurchaseComp" class="mt-4">
  <div
    *ngIf="transactionData.length > 0"
    class="custom-transaction-card w-100 mb-4"
  >
    <!-- Header Banner -->
    <div
      class="header-banner text-white px-3 py-2 rounded-top"
      [class.pointer]="fromPurchaseComp"
      (click)="onCollapseIconClick()"
    >
      <div>Transaction ID - {{ transactionData[0].transactionId }}</div>
      @if (fromPurchaseComp) {
        <div class="me-3">
          <i
            nz-icon
            [nzType]="'down'"
            nzTheme="outline"
            (click)="onCollapseIconClick()"
            class="expand-icon"
          ></i>
        </div>
      }
    </div>

    <!-- Card Body Content -->
    <div class="p-3 rounded-bottom">
      <div
        class="d-flex justify-content-between align-items-start flex-wrap mb-3"
      >
        <div>
          <h4 class="fw-bold mb-1">
            {{ transactionData[0].referenceId || 'N/A' }}
          </h4>
          <div class="text-muted">
            Date -
            {{
              transactionData[0].transactionDate | date: 'dd-MM-yyyy, hh:mm a'
            }}
          </div>
        </div>
        <button nz-button nzType="primary" nzGhost (click)="emailInvoice()">
          Email Invoice
        </button>
      </div>

      <div class="d-flex flex-wrap gap-4 mb-3">
        <!--Commenting for Demo purpose -->
        <!-- <button nz-button nzType="primary" nzGhost (click)="downloadDocument()">
       Download
        </button> -->
        <button nz-button nzType="primary" nzGhost (click)="viewDocuments()">
          View Documents
        </button>
      </div>

      <!-- Data Grid Table -->
      <div class="wrapper mx-4">
        <app-data-grid
          [tableData]="currentTransactionWithOrder?.documents || []"
          [tableColumns]="tableColumns"
          [loading]="isLoading"
          [showPagination]="false"
          [frontPagination]="false"
          [showSizeChanger]="true"
          [showQuickJumper]="true"
          [showTotal]="true"
          [borderedGrid]="true"
          [headerBgColor]="'var(--dark-sky-blue)'"
          [color]="'#000'"
          [scrollY]="null"
          [headerTextAlign]="'start'"
          (tableDataClick)="onTableDataClick($event)"
        ></app-data-grid>
      </div>
    </div>
    <div class="total">
      <p class="text-muted"></p>
      <p class="total-amount fs-2">Total {{ totalAmount | currency: 'USD' }}</p>
    </div>
  </div>
</div>
