<!-- Add User Form -->
<form
  nz-form
  nzLayout="vertical"
  data-testid="add-user-form"
  *ngIf="addUserForm"
>
  <div class="company-form-wrapper col-md-12">
    <div class="row company-form">
      <div
        class="row"
        *ngFor="let userGroup of addUserForm.controls; let i = index"
        [formGroup]="$any(userGroup)"
        [attr.data-test-id]="'user-form-group-' + i"
      >
        <div class="col-md-12">
          <nz-row class="row mb-3" nzGutter="16">
            <nz-col class="col-md-6" [nzSpan]="12"></nz-col>
            <nz-col
              class="col-md-6 d-flex align-items-end justify-content-end"
              [nzSpan]="12"
            >
              <button
                nz-button
                nzType="primary"
                type="button"
                (click)="isEditingUser ? saveEditUser() : addAnotherUser()"
                [disabled]="!isAddUserValid()"
                data-testid="add-user-button"
              >
                {{ isEditingUser ? 'Save Changes' : 'Add' }}
              </button>
              <button
                nz-button
                nzType="default"
                type="button"
                *ngIf="isEditingUser"
                (click)="cancelEditUser()"
                class="ml-2"
                data-testid="cancel-edit-button"
              >
                Cancel
              </button>
            </nz-col>
          </nz-row>

          <nz-row class="row mb-3 align-items-end" nzGutter="16">
            <nz-col
              class="col-md-6 d-flex flex-column align-items-start profile-image"
              [nzSpan]="12"
            >
              <div class="image-wrapper position-relative">
                <img
                  [src]="
                    userGroup.get('profilePictureUrl')?.value ||
                    'assets/profileImage.png'
                  "
                  alt="User Image"
                  class="rounded-circle mb-2"
                  data-testid="user-profile-image"
                />
                <div
                  class="camera-icon"
                  (click)="triggerFileInput(i)"
                  data-testid="camera-icon"
                  aria-label="Upload profile image"
                >
                  <i nz-icon nzType="camera" nzTheme="outline"></i>
                </div>
                <input
                  type="file"
                  [id]="'fileInput-' + i"
                  accept="image/*"
                  style="display: none"
                  (change)="onFileSelected($event, i)"
                  data-testid="user-profile-upload"
                />
              </div>
            </nz-col>
            <nz-col class="col-md-6" [nzSpan]="12">
              <nz-form-item>
                <nz-form-label
                  class="form-label"
                  [nzRequired]="true"
                  nzFor="useremailId"
                >
                  Email ID <span class="text-danger">*</span>
                </nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorTip('email')">
                  <input
                    nz-input
                    type="email"
                    class="form-control form-field"
                    placeholder="Enter email id"
                    formControlName="email"
                    id="useremailId"
                    data-testid="user-email-input"
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row class="row mb-3" nzGutter="16">
            <nz-col class="col-md-6" [nzSpan]="12">
              <nz-form-item>
                <nz-form-label
                  class="form-label"
                  [nzRequired]="true"
                  nzFor="firstName"
                >
                  First Name <span class="text-danger">*</span>
                </nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorTip('firstName')">
                  <input
                    nz-input
                    type="text"
                    class="form-control form-field"
                    placeholder="Enter first name"
                    formControlName="firstName"
                    id="firstName"
                    data-testid="user-first-name-input"
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>

            <nz-col class="col-md-6" [nzSpan]="12">
              <nz-form-item>
                <nz-form-label
                  class="form-label"
                  [nzRequired]="true"
                  nzFor="contactNumber"
                >
                  Telephone <span class="text-danger">*</span>
                </nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorTip('contactNumber')">
                  <input
                    nz-input
                    type="text"
                    class="form-control form-field"
                    placeholder="Enter telephone"
                    formControlName="contactNumber"
                    id="contactNumber"
                    data-test-id="user-telephone-input"
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>

          <nz-row class="row mb-3" nzGutter="16">
            <nz-col class="col-md-6" [nzSpan]="12">
              <nz-form-item>
                <nz-form-label
                  class="form-label"
                  [nzRequired]="true"
                  nzFor="lastName"
                >
                  Last Name <span class="text-danger">*</span>
                </nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorTip('lastName')">
                  <input
                    nz-input
                    type="text"
                    class="form-control form-field"
                    placeholder="Enter last name"
                    formControlName="lastName"
                    id="lastName"
                    data-testid="user-last-name-input"
                  />
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <nz-col class="col-md-6" [nzSpan]="12">
              <nz-form-item>
                <nz-form-label
                  class="form-label"
                  [nzRequired]="true"
                  nzFor="roleName"
                >
                  Role
                </nz-form-label>
                <nz-form-control [nzErrorTip]="getErrorTip('roleId')">
                  <nz-select
                    class="form-control form-field"
                    formControlName="roleId"
                  >
                    <nz-option
                      *ngFor="let role of roles$ | async"
                      [nzValue]="role.id"
                      placeholder="Select a role"
                      [nzLabel]="role.displayText ?? null"
                    >
                    </nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
        </div>
      </div>
    </div>
  </div>
</form>

<!-- Added Users Table -->
<div
  class="col-md-12 mt-4"
  style="display: flex; justify-content: center"
  *ngIf="showDataGrid"
>
  <div>
    <app-data-grid
      class="datagrid"
      [tableColumns]="addedUsersTableColumns"
      [tableData]="tableData"
      [loading]="isLoading"
      [showPagination]="false"
      [defaultSortParam]="'firstName'"
      (tableDataClick)="onTableDataClickUser($event)"
      data-testid="added-users-table"
    >
    </app-data-grid>
  </div>
</div>
