import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
} from '@angular/core';
import {
  FormArray,
  FormGroup,
  FormBuilder,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Observable, of, Subscription, forkJoin } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzIconModule } from 'ng-zorro-antd/icon';

import { DataGridComponent } from '../../data-grid/data-grid.component';
import { SharedLookupService } from '../../../core/services/shared-lookup.service';
import { NotificationService } from '../../../core/services/notification.service';
import {
  UserCreationService,
  UserCreationRequest,
  UserCreationResult,
} from '../../../core/services/user-creation.service';
import { COMMON_STRINGS, PHONE_REGEX } from '../../../core/constants/common';
import { ADD_DELETE_USER_TABLE_COLUMNS } from '../../../core/tableColumns/users.column';
import { IUserFields } from '../../../core/interface/user-fields';
import { ITableDataClickOutput } from '../../../core/interface/table';
import { RoleDTO, CompanyDTO } from '../../../api-client';

@Component({
  selector: 'app-company-user-onboard-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzSelectModule,
    NzTableModule,
    NzModalModule,
    NzIconModule,
    DataGridComponent,
  ],
  templateUrl: './company-user-onboard-form.component.html',
  styleUrl: './company-user-onboard-form.component.scss',
})
export class CompanyUserOnboardFormComponent implements OnInit, OnDestroy {
  @Input() tableData: IUserFields[] = [];

  @Output() tableDataChange = new EventEmitter<IUserFields[]>();
  @Output() userAdded = new EventEmitter<void>();
  @Output() userEdited = new EventEmitter<void>();
  @Output() userDeleted = new EventEmitter<string>();

  addUserForm!: FormArray;
  addedUsers: IUserFields[] = [];
  showDataGrid = false;
  isEditingUser = false;
  editingUserEmail: string | null = null;
  isLoading = false;
  roles$: Observable<RoleDTO[]> = of([]);
  roles: RoleDTO[] = [];
  addedUsersTableColumns = ADD_DELETE_USER_TABLE_COLUMNS;
  selectedFiles = new Map<number, File>(); // Map user index to selected file

  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private sharedLookupService: SharedLookupService,
    private notification: NotificationService,
    private userCreationService: UserCreationService,
  ) {
    this.createUserForm();
  }

  private createUserForm(): void {
    this.addUserForm = this.fb.array([this.createUserFormGroup()]);
  }

  private createUserFormGroup(): FormGroup {
    return this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      contactNumber: [
        '',
        [Validators.required, Validators.pattern(PHONE_REGEX)],
      ],
      roleId: ['', Validators.required],
      profilePictureUrl: [null],
    });
  }

  ngOnInit(): void {
    console.log('in company user onboard form');
    this.fetchRoles();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  fetchRoles(): void {
    this.isLoading = true;
    this.roles$ = this.sharedLookupService.fetchRoles().pipe(
      map((roles: RoleDTO[]) => {
        this.roles = roles;
        this.isLoading = false;
        return this.roles;
      }),
      catchError(() => {
        this.isLoading = false;
        return of([]);
      }),
    );
  }

  addAnotherUser(): void {
    if (!this.isAddUserValid()) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      return;
    }

    const userFormGroup = this.addUserForm.at(0) as FormGroup;
    const formValue = userFormGroup.value;

    if (!this.isEmailUnique(formValue.email)) {
      this.notification.error('Email already exists in the list');
      return;
    }

    const roleName = this.getRoleName(formValue.roleId);
    const selectedFile = this.selectedFiles.get(0);

    const newUser: IUserFields = {
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      email: formValue.email,
      contactNumber: formValue.contactNumber,
      roleId: formValue.roleId,
      roledisplaytext: roleName,
      fullName: `${formValue.firstName} ${formValue.lastName}`,
      isActive: true,
      profilePictureUrl: formValue.profilePictureUrl || null,
    };

    if (selectedFile) {
      (newUser as IUserFields & { selectedFile: File }).selectedFile =
        selectedFile;
    }

    this.addedUsers = [...this.addedUsers, newUser];
    this.tableData = [...this.tableData, newUser];
    this.tableDataChange.emit(this.tableData);
    this.showDataGrid = true;

    userFormGroup.reset();
    this.selectedFiles.delete(0);
    this.userAdded.emit();
    this.notification.success(
      `User ${newUser.fullName} added to list successfully`,
    );
  }

  editUser(email: string): void {
    const user = this.addedUsers.find((u) => u.email === email);
    if (user) {
      const userFormGroup = this.addUserForm.at(0) as FormGroup;
      userFormGroup.patchValue({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        contactNumber: user.contactNumber,
        roleId: user.roleId,
        profilePictureUrl: user.profilePictureUrl,
      });

      if ((user as IUserFields & { selectedFile: File }).selectedFile) {
        this.selectedFiles.set(
          0,
          (user as IUserFields & { selectedFile: File }).selectedFile,
        );
      }

      this.isEditingUser = true;
      this.editingUserEmail = email;
    }
  }

  cancelEditUser(): void {
    const userFormGroup = this.addUserForm.at(0) as FormGroup;
    userFormGroup.reset();
    this.selectedFiles.delete(0);
    this.isEditingUser = false;
    this.editingUserEmail = null;
  }

  saveEditUser(): void {
    if (!this.isAddUserValid()) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      return;
    }

    const userFormGroup = this.addUserForm.at(0) as FormGroup;
    const formValue = userFormGroup.value;

    if (
      formValue.email !== this.editingUserEmail &&
      !this.isEmailUnique(formValue.email)
    ) {
      this.notification.error('Email already exists in the list');
      return;
    }

    const roleName = this.getRoleName(formValue.roleId);
    const selectedFile = this.selectedFiles.get(0);

    const updatedUser: IUserFields = {
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      email: formValue.email,
      contactNumber: formValue.contactNumber,
      roleId: formValue.roleId,
      roledisplaytext: roleName,
      fullName: `${formValue.firstName} ${formValue.lastName}`,
      isActive: true,
      profilePictureUrl: formValue.profilePictureUrl || null,
    };

    if (selectedFile) {
      (updatedUser as IUserFields & { selectedFile: File }).selectedFile =
        selectedFile;
    }

    this.addedUsers = this.addedUsers.map((user) =>
      user.email === this.editingUserEmail ? updatedUser : user,
    );
    this.tableData = this.tableData.map((user) =>
      user.email === this.editingUserEmail ? updatedUser : user,
    );

    this.tableDataChange.emit(this.tableData);

    userFormGroup.reset();
    this.isEditingUser = false;
    this.editingUserEmail = null;
    this.userEdited.emit();
    this.notification.success('User updated successfully');
  }

  onTableDataClickUser(data: ITableDataClickOutput<IUserFields>): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'edit':
        this.editUser(rowData.email || '');
        break;
      case 'delete':
        this.deleteUser(rowData.email || '');
        break;
    }
  }

  deleteUser(email: string): void {
    this.addedUsers = this.addedUsers.filter((user) => user.email !== email);
    this.tableData = this.tableData.filter((user) => user.email !== email);
    this.tableDataChange.emit(this.tableData);

    if (this.addedUsers.length === 0) {
      this.showDataGrid = false;
    }

    this.userDeleted.emit(email);
    this.notification.success('User removed from list');
  }

  isAddUserValid(): boolean {
    const userFormGroup = this.addUserForm.at(0) as FormGroup;
    return userFormGroup.valid;
  }

  isEmailUnique(email: string): boolean {
    return !this.addedUsers.some(
      (user) => user.email?.toLowerCase() === email.toLowerCase(),
    );
  }

  getRoleName(roleId: number): string {
    const role = this.roles.find((r) => r.id === roleId);
    return role?.displayText || 'N/A';
  }

  getErrorTip(controlName: string): string | undefined {
    const userFormGroup = this.addUserForm.at(0) as FormGroup;
    const control = userFormGroup.get(controlName);
    if (control?.touched && control?.hasError('required')) {
      return `${controlName.charAt(0).toUpperCase() + controlName.slice(1)} is required`;
    }
    if (
      controlName === 'email' &&
      control?.touched &&
      control?.hasError('email')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidEmail;
    }
    if (
      controlName === 'contactNumber' &&
      control?.touched &&
      control?.hasError('pattern')
    ) {
      return COMMON_STRINGS.warningMessages.enterValidPhoneNumber;
    }
    return undefined;
  }

  triggerFileInput(index: number): void {
    const fileInput = document.getElementById(
      `fileInput-${index}`,
    ) as HTMLInputElement;
    fileInput?.click();
  }

  onFileSelected(event: Event, userIndex = 0): void {
    this.userCreationService.handleFileSelection(
      event,
      (file: File, dataUrl: string) => {
        this.selectedFiles.set(userIndex, file);
        const userFormGroup = this.addUserForm.at(userIndex) as FormGroup;
        userFormGroup.get('profilePictureUrl')?.setValue(dataUrl);
      },
    );
  }

  getSelectedFile(userIndex = 0): File | undefined {
    return this.selectedFiles.get(userIndex);
  }

  clearSelectedFile(userIndex = 0): void {
    this.selectedFiles.delete(userIndex);
    const userFormGroup = this.addUserForm.at(userIndex) as FormGroup;
    userFormGroup.get('profilePictureUrl')?.setValue(null);
  }

  getUsersForSubmission(): (IUserFields & { selectedFile?: File })[] {
    return this.addedUsers.map((user) => ({
      ...user,
      selectedFile:
        (user as IUserFields & { selectedFile?: File }).selectedFile ||
        undefined,
    }));
  }

  getUsersCount(): number {
    return this.addedUsers.length;
  }

  hasUsers(): boolean {
    return this.addedUsers.length > 0;
  }

  createAllUsersForCompany(
    companyId: number,
    companyData: CompanyDTO,
  ): Observable<UserCreationResult[]> {
    if (this.addedUsers.length === 0) {
      return of([]);
    }

    const userCreationObservables = this.addedUsers.map((user) => {
      const selectedFile = (user as IUserFields & { selectedFile?: File })
        .selectedFile;

      const request: UserCreationRequest = {
        formData: {
          firstName: user.firstName!,
          lastName: user.lastName!,
          email: user.email!,
          contactNumber: user.contactNumber!,
          roleId: user.roleId!,
          profilePictureUrl: user.profilePictureUrl,
        },
        companyId: companyId,
        company: companyData,
        selectedFile: selectedFile || null,
        userType: 'COMPANY',
      };

      return this.userCreationService.createUserWithProfilePicture(request);
    });

    return forkJoin(userCreationObservables);
  }
}
