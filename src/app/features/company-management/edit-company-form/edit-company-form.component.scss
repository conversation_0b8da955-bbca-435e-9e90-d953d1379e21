// Edit Company Form specific styles
.company-edit-form-buttons {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.save-edit-button {
  height: 36px;
  width: 170px;
  background-color: var(--primary-button-color);
  border-radius: 7px;
  color: #FFFFFF;
}

.edit-company-form-field {
  height: 45px;
  border: 1px solid #d8cece;
  border-radius: 7px;
  width: 27rem;
}

.left-column {
  padding-left: 20px;
  padding-right: 50px;
}

.right-column {
  padding-left: 50px;
  padding-right: 20px;
}

::ng-deep .edit-drawer .ant-form-vertical .ant-form-item-label {
  font-weight: bold;
}

::ng-deep .ant-drawer-title {
  font-size: 22px !important;
  font-weight: bold;
}

::ng-deep .ant-form-item {
  margin-bottom: 0;
}

.form-field {
  height: 45px;
}

.text-danger {
  font-size: 12px;
  margin-top: 5px;
  color: #ff4d4f;
}

input.is-invalid {
  background-image: none !important;
}

.nz-form-field {
  height: 45px;
}

:host ::ng-deep .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f !important;
}

:host ::ng-deep .ant-form-item-explain-error {
  color: #ff4d4f;
}