.documents-container {
  padding: 20px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.button-row {
  margin-top: 20px;
}

.text-danger {
  font-size: 12px;
  margin-top: 5px;
}

.company-form-wrapper {
  display: flex;
  justify-content: center;
}

.company-form {
  position: relative;
  width: 100%;
  background: white;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background: #daecff;
}

.next-button-wrapper {
  padding: 1rem;
  display: flex;
  justify-content: flex-end;
}

.form-field {
  height: 45px;
}

.edit-company-form-field {
  height: 45px;
  border: 1px solid #d8cece;
  border-radius: 7px;
  width: 27rem;
}

.left-column {
  padding-left: 20px;
  padding-right: 50px;
}

.right-column {
  padding-left: 50px;
  padding-right: 20px;
}

.cancel-button {
  bottom: 75px;
  left: 15%;
  min-width: 100px;
  background-color: var(--primary-button-color);
  border: none;
  color: #FFFFFF;
  border-radius: 5px;
  height: 40px;
  z-index: 1000;
  cursor: pointer;
  margin-right: 86%;
}

.previous-button {
  bottom: 75px;
  left: 15%;
  min-width: 100px;
  background-color: var(--primary-button-color);
  border: none;
  color: #FFFFFF;
  border-radius: 5px;
  height: 40px;
  z-index: 1000;
  cursor: pointer;
}

.next-button {
  bottom: 75px;
  right: 4%;
  min-width: 100px;
  background-color: var(--primary-button-color);
  border: none;
  color: #FFFFFF;
  border-radius: 5px;
  height: 40px;
  z-index: 1000;
  cursor: pointer;
}

.previous-button:hover,
.next-button:hover,
.add-button:hover,
.add-another-button:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}


.form-field::placeholder {
  opacity: 0.3;
}

.navigation-buttons {
  display: flex;
  z-index: 1000;
  margin:  2rem 6rem ;
}

.add-button {
  width: 90px;
  height: 40px;
  background-color: var(--primary-button-color);
  z-index: 1000;
  border-radius: 5px;
  border: none;
  cursor: pointer;
}

.add-another-button {
  width: 150px;
  height: 40px;
  background-color: transparent;
  color: var(--primary-button-color);
  border: 2px solid var(--primary-button-color);
  border-radius: 5px;
  z-index: 1000;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  cursor: pointer;
}

.add-another-button:hover {
  background-color: var(--primary-button-color);
  color: #FFFFFF;
}

.profile-image{
  padding-left: 30px;
  padding-bottom: 30px;
}

input.is-invalid {
  background-image: none !important; 
}

.onboarding-wrapper{
  padding: 0 4rem;
}

.nz-form-field{
  height: 45px;
}

::ng-deep .company-creation-tabs .ant-tabs-nav-list {
  display: flex;
  background: #0F2E50;
  height: 70px;
  width: 100%;
  border-radius: 6px;
  margin-top: 50px;
}

::ng-deep .company-creation-tabs .ant-tabs-tab .ng-star-inserted {
  background: none;
  border: none;
  font-size: 14px;
  color: #B2D6FC;
  cursor: pointer;
  position: relative;
  transition: all 0.3s;
  flex: 1;
  text-align: center;
  background-color: red;
}

::ng-deep .company-creation-tabs.ant-tabs>.ant-tabs-nav .ant-tabs-nav-list, .ant-tabs>div>.ant-tabs-nav .ant-tabs-nav-list {
  justify-content: space-around;
  align-items: center;
  transition: transform .3s;
  color: #B2D6FC;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}


::ng-deep .company-creation-tabs .ant-tabs-ink-bar {
  background-color: #ffffff !important;
  height: 1px;
  border-radius: 1px;
  margin-bottom: 20px;
}

::ng-deep .company-creation-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #FFFFFF;
  text-shadow: 0 0 .25px currentcolor;
}

::ng-deep .company-creation-tabs .ant-tabs>.ant-tabs-nav, .ant-tabs>div>.ant-tabs-nav {
  align-items: center;
  margin: 0;
}


::ng-deep .ant-tabs-top>.ant-tabs-nav {
  margin: 0;
}

:host ::ng-deep .company-creation-tabs .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f !important;
}

:host ::ng-deep .company-creation-tabs .ant-form-item-explain-error {
  color: #ff4d4f;
}

.text-danger {
  color: #ff4d4f;
}

::ng-deep .ant-card-bordered{
  background: #FFFFFF;
}

app-data-grid ::ng-deep .ant-tabs-nav-wrap{
  background: red;
}

.company-tabs-wrapper {
  padding-top: 20px;
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.controls-section{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.company-stats-card {
  margin-bottom: 20px; 
  width:40%;
}

:host ::ng-deep .company-stats-card .ant-card {
  border: 1px solid #d9d9d9; 
  border-radius: 8px; 
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); 
}

:host ::ng-deep .company-stats-card .ant-card-body {
  padding: 16px; 
}

.stats-container {
  display: flex;
  justify-content: space-between; 
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column; 
  align-items: flex-start;
}

.stat-label {
  font-size: 14px; 
  color: #666; 
  margin-bottom: 4px; 
  font-weight: bold; 

}

.stat-value {
  font-size: 20px;
  color: #000; 
}

:host ::ng-deep .company-tabs {
  border: 1px solid #000;
  border-radius: 8px;
  overflow: hidden;
  width: 500px;
}

:host ::ng-deep .company-tabs .ant-tabs-nav {
  margin: 0;
  padding: 0 20px;
  background-color: #f5f5f5;
}

:host ::ng-deep .company-tabs .ant-tabs-nav-list {
  display: flex;
  justify-content: flex-start;
  gap: 16px;
  width: 100%;
}

:host ::ng-deep .company-tabs .ant-tabs-tab {
  background-color: transparent;
  padding: 12px 24px;
  height: 48px;
  display: flex;
  align-items: center;
  transition: background-color 0.3s, color 0.3s;
  border-bottom: none;
}

:host ::ng-deep .company-tabs .ant-tabs-tab-active {
  background-color: #1890ff;
  border-radius: 8px 8px 0 0;
}

:host ::ng-deep .company-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: white !important;
}

:host ::ng-deep .company-tabs .ant-tabs-tab-btn {
  color: black;
  font-weight: bold;
}

:host ::ng-deep .company-tabs .ant-tabs-ink-bar {
  display: none !important;
}

:host ::ng-deep .company-tabs .ant-tabs-content-holder {
  border-top: 1px solid #000;
  padding: 16px;
}

.table-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.add-company-button {
  background-color: var(--primary-button-color);
  color: white;
  height: 40px;
  border: none;
  width: 150px;
  margin-left: 30px;
  &:hover {
    background-color: var(--primary-button-color);
  }
}

::ng-deep .company-tabs .ant-card-bordered{
  width: 500px;
}

.company-container{
  padding-left: 4rem;
  padding-right: 4rem;
}

.checkbox-group {
  display: flex;
  gap: 20px;
  align-items: center;

}

::ng-deep .ant-checkbox+span{
  font-weight: bold; 

}

.datagrid {
  max-width: 90%;
}
.text-blue {
  color: #007bff;
}

::ng-deep .ant-checkbox-inner {
  width: 20px;
  height: 20px;
  position: relative; 
}

.ant-checkbox-inner::after {
  content: '';
  position: absolute;
  left: 7px;    
  top: 4px;     
  width: 6px;   
  height: 12px; 
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  box-sizing: border-box;
}

::ng-deep .ant-checkbox-checked .ant-checkbox-inner {
  background-color: black;
  border-color: black;
}


.user-grid {
  width: 100%;
  font-size: 14px;
  color: #333;

  .user-grid-header,
  .user-grid-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr; 
    gap: 16px; 
    padding: 8px 0;
    align-items: center;
  }

  .user-grid-header {
    font-weight: bold;
    color: #666;
    border-bottom: 1px solid #e8e8e8; 
  }

  .user-grid-cell {
    word-break: break-word; 
  }

  .user-grid-cell a {
    color: #1890ff; 
    cursor: pointer;
    text-decoration: none;
  }

  .user-grid-cell a:hover {
    text-decoration: underline;
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: #999;
  }
}

.next-button:disabled,
.previous-button:disabled,
.add-button:disabled {
  background-color: #d3d3d3; 
  color: #a9a9a9; 
  border-color: #d3d3d3; 
  cursor: not-allowed;
}

.next-button,
.previous-button,
.add-button {
  color: #ffffff; 
}

.company-edit-form-buttons{
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.save-edit-button{
  height: 36px;
  width: 170px;
  background-color: var(--primary-button-color);
  border-radius: 7px;
  color: #FFFFFF;
}

.edit-drawer .ant-form-vertical .ant-form-item-label{
  font-weight: bold ;
}

 .ant-drawer-title{
  font-size: 22px !important;
  font-weight: bold ;
}

.ant-form-item{
  margin-bottom: 0;
}
