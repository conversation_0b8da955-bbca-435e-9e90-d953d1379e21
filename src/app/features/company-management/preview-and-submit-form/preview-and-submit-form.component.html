<!-- Preview & Submit Section -->
<div class="company-form-wrapper" data-testid="preview-section">
  <div class="row company-form">
    <!-- Company Information Card -->
    <nz-card data-testid="company-info-card">
      <div class="d-flex justify-content-between align-items-center">
        <h4>Company Information</h4>
        <nz-icon
          nzType="edit"
          nzTheme="outline"
          class="nz-icon-style"
          (click)="onEditSection(OnboardingStep.CompanyDetails)"
        />
      </div>
      <div class="preview-item" data-testid="preview-company-name">
        <strong>Company Name:</strong>
        {{ onboardingState?.companyDetails?.name || 'N/A' }}
      </div>
      <div class="preview-item" data-testid="preview-abn">
        <strong>ABN:</strong>
        {{ onboardingState?.companyDetails?.abn || 'N/A' }}
      </div>
      <div class="preview-item" data-testid="preview-acn">
        <strong>ACN:</strong>
        {{ onboardingState?.companyDetails?.acn || 'N/A' }}
      </div>
      <div class="preview-item" data-testid="preview-company-address">
        <strong>Full Company Address:</strong>
        {{
          (onboardingState?.companyDetails?.addressLine1 || '') +
            (onboardingState?.companyDetails?.addressLine2
              ? ', ' + onboardingState?.companyDetails?.addressLine2
              : '') +
            (onboardingState?.companyDetails?.suburb
              ? ', ' + onboardingState?.companyDetails?.suburb
              : '') +
            (onboardingState?.companyDetails?.state
              ? ', ' + onboardingState?.companyDetails?.state
              : '') +
            (onboardingState?.companyDetails?.postalCode
              ? ' ' + onboardingState?.companyDetails?.postalCode
              : '') || 'N/A'
        }}
      </div>
    </nz-card>

    <!-- User Information Card -->
    <nz-card data-testid="user-info-card">
      <div class="d-flex justify-content-between align-items-center">
        <h4>User Information</h4>
        <nz-icon
          nzType="edit"
          nzTheme="outline"
          class="nz-icon-style"
          (click)="onEditSection(OnboardingStep.AddUser)"
        />
      </div>
      <div class="user-grid">
        <!-- Header Row -->
        <div class="user-grid-header" data-testid="user-grid-header">
          <div class="user-grid-cell">User Added</div>
          <div class="user-grid-cell">Email ID</div>
          <div class="user-grid-cell">Role</div>
        </div>
        <!-- Data Rows -->
        <div
          class="user-grid-row"
          *ngFor="let user of tableData; let i = index"
          [attr.data-test-id]="'user-grid-row-' + i"
        >
          <div
            class="user-grid-cell"
            [attr.data-test-id]="'user-grid-name-' + i"
          >
            {{ (user.firstName || '') + ' ' + (user.lastName || '') || 'N/A' }}
          </div>
          <div
            class="user-grid-cell"
            [attr.data-test-id]="'user-grid-email-' + i"
          >
            {{ user.email || 'N/A' }}
          </div>
          <div
            class="user-grid-cell"
            [attr.data-test-id]="'user-grid-role-' + i"
          >
            {{ user.roledisplaytext || 'N/A' }}
          </div>
        </div>
      </div>
    </nz-card>

    <!-- Billing Information Card -->
    <nz-card data-testid="billing-info-card">
      <div class="d-flex justify-content-between align-items-center">
        <h4>Billing Information</h4>
        <nz-icon
          nzType="edit"
          nzTheme="outline"
          class="nz-icon-style"
          (click)="onEditSection(OnboardingStep.AddPayment)"
        />
      </div>
      <div class="preview-item" data-testid="preview-billing-name">
        <strong>Card Details:</strong>
        {{ onboardingState?.billingDetails?.name || 'N/A' }}
      </div>
      <div class="preview-item" data-testid="preview-billing-address">
        <strong>Billing Address:</strong>
        {{
          (onboardingState?.billingDetails?.addressLine1 || '') +
            (onboardingState?.billingDetails?.addressLine2
              ? ', ' + onboardingState?.billingDetails?.addressLine2
              : '') +
            (onboardingState?.billingDetails?.suburb
              ? ', ' + onboardingState?.billingDetails?.suburb
              : '') +
            (onboardingState?.billingDetails?.state
              ? ', ' + onboardingState?.billingDetails?.state
              : '') +
            (onboardingState?.billingDetails?.postalCode
              ? ' ' + onboardingState?.billingDetails?.postalCode
              : '') || 'N/A'
        }}
      </div>
    </nz-card>
  </div>
</div>

<!-- Submit Button - positioned like navigation buttons -->
<div class="navigation-buttons mt-6 d-flex justify-content-end">
  <button
    type="button"
    class="next-button"
    (click)="onCreateCompany()"
    [disabled]="!isCreateButtonEnabled"
    data-testid="create-company-button"
  >
    Create
  </button>
</div>
