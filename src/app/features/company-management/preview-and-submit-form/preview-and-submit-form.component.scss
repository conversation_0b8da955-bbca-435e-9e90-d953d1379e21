// Preview and Submit Form specific styles
.company-form-wrapper {
  display: flex;
  justify-content: center;
}

.company-form {
  position: relative;
  width: 100%;
  background: white;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background: #daecff;
}

.preview-item {
  margin-bottom: 10px;
  padding: 5px 0;
}

.preview-item strong {
  color: #333;
  margin-right: 10px;
}

::ng-deep .ant-card-bordered {
  background: #FFFFFF;
  margin-bottom: 20px;
}

::ng-deep .ant-card-head {
  background-color: #f5f5f5;
  border-bottom: 1px solid #d9d9d9;
}

::ng-deep .ant-card-head-title {
  font-weight: bold;
  color: #333;
}

.nz-icon-style {
  cursor: pointer;
  color: var(--primary-button-color);
  font-size: 16px;
}

.nz-icon-style:hover {
  color: #0056b3;
}

// User table styles for preview
.user-preview-table {
  width: 100%;
  margin-top: 10px;
}

.user-preview-table th,
.user-preview-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
}

.user-preview-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #666;
}

.user-preview-table tr:hover {
  background-color: #f9f9f9;
}

// Create button styles
.create-company-button {
  background-color: var(--primary-button-color);
  color: white;
  height: 40px;
  border: none;
  border-radius: 5px;
  padding: 0 20px;
  font-weight: bold;
}

.create-company-button:hover {
  background-color: #0056b3;
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

.create-company-button:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  cursor: not-allowed;
  transform: none;
}