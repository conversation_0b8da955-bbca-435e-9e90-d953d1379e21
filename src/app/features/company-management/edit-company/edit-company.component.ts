import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Observable, of, map, catchError } from 'rxjs';
import {
  ApiResponseCompanyDTO,
  CompanyDTO,
  CompanyRequestDTO,
  StateDTO,
  ZipCodeDTO,
} from '../../../api-client';
import { HttpErrorResponse } from '@angular/common/http';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { SharedLookupService } from '../../../core/services/shared-lookup.service';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { CommonModule } from '@angular/common';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';

@Component({
  selector: 'app-edit-company',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
  ],
  templateUrl: './edit-company.component.html',
  styleUrls: ['./edit-company.component.scss'],
})
export class EditCompanyComponent implements OnInit {
  @Input() isDrawerVisible = false;
  @Input() set selectedCompany(value: ICompanyFields | null) {
    this._selectedCompany = value;
    if (value) {
      this.initializeForm(value);
    } else {
      this.editCompanyForm.reset();
      this.postalCode$ = of([]);
    }
  }
  get selectedCompany(): ICompanyFields | null {
    return this._selectedCompany;
  }
  private _selectedCompany: ICompanyFields | null = null;

  @Output() OnClose = new EventEmitter<void>();
  @Output() companyUpdated = new EventEmitter<CompanyDTO>();

  editCompanyForm: FormGroup;
  isLoading = false;
  states$: Observable<StateDTO[]> = of([]);
  postalCode$: Observable<ZipCodeDTO[]> = of([]);

  constructor(
    private fb: FormBuilder,
    private registerService: RegisterService,
    private notification: NotificationService,
    private sharedLookupService: SharedLookupService,
  ) {
    this.editCompanyForm = this.fb.group({
      name: ['', Validators.required],
      addressLine1: ['', Validators.required],
      addressLine2: [''],
      suburb: [''],
      state: ['', Validators.required],
      postalCode: [{ value: '', disabled: true }, Validators.required],
      accountsContactName: [''],
      billingEmail: ['', [Validators.required, Validators.email]],
    });
  }

  ngOnInit(): void {
    this.fetchStates();
    this.editCompanyForm.get('state')?.valueChanges.subscribe((stateId) => {
      if (stateId) {
        this.fetchZipcodes(stateId);
        this.editCompanyForm.get('postalCode')?.enable({ emitEvent: false });
      } else {
        this.postalCode$ = of([]);
        this.editCompanyForm.get('postalCode')?.reset();
        this.editCompanyForm.get('postalCode')?.disable({ emitEvent: false });
      }
    });
  }

  initializeForm(rowData: ICompanyFields): void {
    this.isLoading = true;
    this.fetchStatesForEdit(rowData).subscribe({
      next: ({ states, selectedStateId }) => {
        this.initializeEditForm(rowData, states, selectedStateId);
        this.isLoading = false;
      },
      error: () => {
        this.isLoading = false;
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchStates,
        );
      },
    });
  }

  private fetchStatesForEdit(
    rowData: ICompanyFields,
  ): Observable<{ states: StateDTO[]; selectedStateId: number | null }> {
    return this.sharedLookupService.fetchStates().pipe(
      map((states: StateDTO[]) => {
        const state = states.find(
          (s) =>
            s.stateName === rowData.primaryAddress?.stateName ||
            s.stateAbbr === rowData.primaryAddress?.stateId ||
            s.id === Number(rowData.primaryAddress?.stateId),
        );
        return { states, selectedStateId: state?.id || null };
      }),
      catchError(() => {
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchStates,
        );
        return of({ states: [], selectedStateId: null });
      }),
    );
  }

  private initializeEditForm(
    rowData: ICompanyFields,
    _states: StateDTO[],
    selectedStateId: number | null,
  ): void {
    const primaryAddress = rowData.primaryAddress || {};

    const formData = {
      name: rowData.name || '',
      addressLine1: primaryAddress.addressLine1 || '',
      addressLine2: primaryAddress.addressLine2 || '',
      suburb: primaryAddress.suburb || '',
      state: selectedStateId || '',
      accountsContactName: rowData.accountsContactName || '',
      billingEmail: rowData.billingEmail || '',
    };

    this.editCompanyForm.patchValue(formData);

    if (selectedStateId) {
      this.editCompanyForm.get('postalCode')?.enable({ emitEvent: false });
      this.setupZipcodesForEdit(rowData, selectedStateId);
    } else {
      this.postalCode$ = of([]);
      this.editCompanyForm.get('postalCode')?.reset();
      this.editCompanyForm.get('postalCode')?.disable({ emitEvent: false });
    }
  }

  private setupZipcodesForEdit(rowData: ICompanyFields, stateId: number): void {
    this.isLoading = true;
    this.sharedLookupService.fetchZipcodes(stateId).subscribe({
      next: (zipcodes: ZipCodeDTO[]) => {
        this.isLoading = false;

        if (zipcodes.length === 0) {
          this.resetAndDisablePostalCodes(
            COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
          );
          return;
        }

        this.postalCode$ = of(zipcodes);

        this.editCompanyForm.get('postalCode')?.enable({ emitEvent: false });

        const matchingZipcode = zipcodes.find(
          (postalCode) =>
            postalCode.id === Number(rowData.primaryAddress?.zipCodeId) ||
            postalCode.zipCode === rowData.primaryAddress?.zipCode,
        );

        if (matchingZipcode) {
          this.editCompanyForm
            .get('postalCode')
            ?.setValue(matchingZipcode.id, { emitEvent: false });
        } else {
          this.notification.error(
            COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
          );
        }
      },
      error: () => {
        this.isLoading = false;
        this.resetAndDisablePostalCodes(
          COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
        );
      },
    });
  }

  saveCompanyDetails(): void {
    if (!this.editCompanyForm.valid || !this.selectedCompany?.id) {
      this.notification.error(COMMON_STRINGS.warningMessages.enterAllFields);
      return;
    }

    const formValue = this.editCompanyForm.getRawValue();
    const companyRequest: CompanyRequestDTO = {
      name: formValue.name,
      primaryAddress: {
        addressLine1: formValue.addressLine1,
        addressLine2: formValue.addressLine2 || undefined,
        suburb: formValue.suburb || undefined,
        stateId: Number(formValue.state),
        zipCodeId: Number(formValue.postalCode),
      },
      billingAddress: {
        addressLine1: formValue.addressLine1,
        addressLine2: formValue.addressLine2 || undefined,
        suburb: formValue.suburb || undefined,
        stateId: Number(formValue.state),
        zipCodeId: Number(formValue.postalCode),
      },
      billingEmail: formValue.billingEmail || '',
      accountsContactName: formValue.accountsContactName || undefined,
      isBillingPrimary: true,
    };

    this.isLoading = true;
    this.registerService
      .updateCompany(this.selectedCompany.id, companyRequest)
      .subscribe({
        next: (response: ApiResponseCompanyDTO) => {
          this.isLoading = false;
          if (response.data) {
            this.companyUpdated.emit(response.data);
            this.notification.success(
              COMMON_STRINGS.successMessages.companyUpdateSuccess,
            );
            this.editDrawerClose();
          }
        },
        error: (error: HttpErrorResponse) => {
          this.isLoading = false;
          this.notification.error(
            error.error?.message ||
              COMMON_STRINGS.warningMessages.companyUpdateFailure.replace(
                '${errorMessage}',
                error.message,
              ),
          );
        },
      });
  }

  editDrawerClose(): void {
    this.isDrawerVisible = false;
    this._selectedCompany = null;
    this.editCompanyForm.reset({
      name: '',
      addressLine1: '',
      addressLine2: '',
      suburb: '',
      state: '',
      postalCode: '',
      accountsContactName: '',
      billingEmail: '',
    });
    this.OnClose.emit();
  }

  fetchStates(): void {
    this.isLoading = true;
    this.states$ = this.sharedLookupService.fetchStates().pipe(
      map((states: StateDTO[]) => {
        this.isLoading = false;
        return states;
      }),
      catchError(() => {
        this.isLoading = false;
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchStates,
        );
        return of([]);
      }),
    );
  }

  fetchZipcodes(stateId: number): Observable<ZipCodeDTO[]> {
    if (!stateId) {
      this.resetAndDisablePostalCodes(
        COMMON_STRINGS.warningMessages.selectValidState,
      );
      return of([]);
    }
    this.isLoading = true;
    return this.sharedLookupService.fetchZipcodes(stateId).pipe(
      map((zipcodes: ZipCodeDTO[]) => {
        this.isLoading = false;
        if (zipcodes.length === 0) {
          this.resetAndDisablePostalCodes(
            COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
          );
        } else {
          this.editCompanyForm.get('postalCode')?.enable({ emitEvent: false });
        }
        return zipcodes;
      }),
      catchError(() => {
        this.isLoading = false;
        this.resetAndDisablePostalCodes(
          COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
        );
        return of([]);
      }),
    );
  }

  private resetAndDisablePostalCodes(warningMessage: string): void {
    this.postalCode$ = of([]);
    this.notification.warning(warningMessage);
    this.editCompanyForm.get('postalCode')?.reset('', { emitEvent: false });
    this.editCompanyForm.get('postalCode')?.disable({ emitEvent: false });
  }
}
