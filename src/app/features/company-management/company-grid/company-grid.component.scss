// Company Grid specific styles
.company-container {
  padding-left: 4rem;
  padding-right: 4rem;
}

.company-tabs-wrapper {
  padding-top: 20px;
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.company-stats-card {
  margin-bottom: 20px;
  width: 40%;
}

:host ::ng-deep .company-stats-card .ant-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:host ::ng-deep .company-stats-card .ant-card-body {
  padding: 16px;
}

.stats-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
  font-weight: bold;
}

.stat-value {
  font-size: 20px;
  color: #000;
}

.checkbox-group {
  display: flex;
  gap: 20px;
  align-items: center;
}

::ng-deep .ant-checkbox+span {
  font-weight: bold;
}

::ng-deep .ant-checkbox-inner {
  width: 20px;
  height: 20px;
  position: relative;
}

.ant-checkbox-inner::after {
  content: '';
  position: absolute;
  left: 7px;
  top: 4px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  box-sizing: border-box;
}

::ng-deep .ant-checkbox-checked .ant-checkbox-inner {
  background-color: black;
  border-color: black;
}

.add-company-button {
  background-color: var(--primary-button-color);
  color: white;
  height: 40px;
  border: none;
  width: 150px;
  margin-left: 30px;
  &:hover {
    background-color: var(--primary-button-color);
  }
}

.datagrid {
  max-width: 90%;
}

.text-blue {
  color: #007bff;
}