// Company Onboard Form specific styles
.company-form-wrapper {
  display: flex;
  justify-content: center;
}

.company-form {
  position: relative;
  width: 100%;
  background: white;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background: #daecff;
}

.form-field {
  height: 45px;
}

.left-column {
  padding-left: 20px;
  padding-right: 50px;
}

.right-column {
  padding-left: 50px;
  padding-right: 20px;
}

.form-field::placeholder {
  opacity: 0.3;
}

.nz-form-field {
  height: 45px;
}

input.is-invalid {
  background-image: none !important;
}

.text-danger {
  font-size: 12px;
  margin-top: 5px;
  color: #ff4d4f;
}

:host ::ng-deep .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f !important;
}

:host ::ng-deep .ant-form-item-explain-error {
  color: #ff4d4f;
}

::ng-deep .ant-card-bordered {
  background: #FFFFFF;
}

// Billing form specific styles
.billing-form {
  .company-form {
    background: #f0f8ff;
  }
}

.billing-checkbox {
  margin-bottom: 20px;
}

.checkbox-label {
  font-weight: bold;
  color: var(--primary-button-color);
}