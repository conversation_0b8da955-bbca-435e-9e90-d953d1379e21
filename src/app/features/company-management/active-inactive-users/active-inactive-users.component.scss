// Active Inactive Users Component specific styles
.inactive-accordian {
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  color: var(--primary-button-color);
}

.active-accordian {
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  color: var(--primary-button-color);
  justify-content: space-between;
}

.chevron {
  margin-left: 8px;
  margin-top: 4px;
}

// User table styles
.user-table-datagrid {
  margin-top: 20px;
}

// Add user button styles
.add-user-button {
  background-color: var(--primary-button-color);
  color: white;
  height: 40px;
  border: none;
  border-radius: 5px;
  padding: 0 20px;
  margin-bottom: 20px;
}

.add-user-button:hover {
  background-color: #0056b3;
}

// Accordion styles
.accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 10px;
}

.accordion-header:hover {
  background-color: #e6f7ff;
}

.accordion-content {
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 4px 4px;
  margin-bottom: 20px;
}

// User count badges
.user-count-badge {
  background-color: var(--primary-button-color);
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  margin-left: 10px;
}