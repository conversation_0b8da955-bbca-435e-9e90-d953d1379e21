<!-- Accordion for Active Users -->
<div class="active-users-accordion mt-3">
  <div class="accordion-header d-flex align-items-center active-accordian">
    <div
      class="d-flex align-items-center"
      (click)="toggleActiveUsersAccordian()"
      role="button"
      aria-label="Toggle active users"
    >
      <span>View Active Users</span>
      <i
        class="fa-solid chevron"
        [ngClass]="
          isActiveUsersAccordianOpen ? 'fa-chevron-right' : 'fa-chevron-up'
        "
      ></i>
    </div>
    <button
      *ngIf="isActiveUsersAccordianOpen"
      nz-button
      nzType="primary"
      class="add-user-button"
      (click)="openAddUserDrawer()"
    >
      Add
    </button>
  </div>
  <div *ngIf="isActiveUsersAccordianOpen" class="accordion-content">
    <div *ngIf="userTableData.length === 0" class="text-center p-3">
      No active users found.
    </div>
    <app-data-grid
      *ngIf="userTableData.length > 0"
      class="user-table-datagrid"
      [tableColumns]="userTableColumns"
      [tableData]="userTableData"
      [loading]="isLoading"
      [defaultSortParam]="'firstName'"
      [showPagination]="false"
      (tableDataClick)="onTableDataClick($event)"
    ></app-data-grid>
  </div>

  <!-- Add User Component -->
  <app-add-user
    [isVisible]="isAddUserDrawerOpen"
    [companyId]="companyId"
    [company]="company"
    [existingUsers]="userTableData"
    (userAdded)="onUserAdded($event)"
    (drawerClosed)="closeAddUserDrawer()"
  >
  </app-add-user>
</div>

<!-- Accordion for Deleted Users -->
<div class="deleted-users-accordion mt-3">
  <div
    class="accordion-header d-flex align-items-center inactive-accordian"
    (click)="toggleDeletedUsersAccordion()"
    role="button"
    aria-label="Toggle deleted users"
  >
    <span>View Inactive Users</span>
    <i
      class="fa-solid chevron"
      [ngClass]="
        isDeletedUsersAccordionOpen ? 'fa-chevron-right' : 'fa-chevron-up'
      "
    ></i>
  </div>
  <div *ngIf="isDeletedUsersAccordionOpen" class="accordion-content">
    <div *ngIf="deletedUserTableData.length === 0" class="text-center p-3">
      No deleted users found.
    </div>
    <app-data-grid
      *ngIf="deletedUserTableData.length > 0"
      class="deleted-user-table-datagrid"
      [tableColumns]="userTableColumns"
      [tableData]="deletedUserTableData"
      [loading]="isLoading"
      [defaultSortParam]="'firstName'"
      [showPagination]="false"
      (tableDataClick)="onTableDataClick($event)"
      [ngClass]="{ 'deleted-users-table': true }"
    ></app-data-grid>
  </div>
</div>
