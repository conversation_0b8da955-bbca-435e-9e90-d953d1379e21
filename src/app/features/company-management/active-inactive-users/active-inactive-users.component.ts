import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { HttpErrorResponse } from '@angular/common/http';
import { of, Subscription, catchError, map } from 'rxjs';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzUploadModule } from 'ng-zorro-antd/upload';
import { DataGridComponent } from '../../data-grid/data-grid.component';

// Import required services and interfaces
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { VIEW_USER_TABLE_COLUMNS } from '../../../core/tableColumns/users.column';
import { IUserFields } from '../../../core/interface/user-fields';
import { ICompanyFields } from '../../../core/interface/company-fields';
import {
  ApiResponsePageUserResponseDTO,
  UserResponseDTO,
} from '../../../api-client';
import { ITableDataClickOutput } from '../../../core/interface/table';
import { AddUserComponent } from '../add-user/add-user.component';

@Component({
  selector: 'app-active-inactive-users',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzTabsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzCardModule,
    NzTableModule,
    DataGridComponent,
    NzModalModule,
    NzUploadModule,
    NzIconModule,
    NzDrawerModule,
    NzSelectModule,
    NzBreadCrumbModule,
    AddUserComponent,
  ],
  templateUrl: './active-inactive-users.component.html',
  styleUrls: [
    './active-inactive-users.component.scss',
    '../view-company/view-company.component.scss',
  ],
})
export class ActiveInactiveUsersComponent implements OnInit, OnDestroy {
  @Input() companyId: number | null = null;
  @Input() company: ICompanyFields | null = null;
  @Output() userUpdated = new EventEmitter<void>();

  // Table configuration and data
  userTableColumns = VIEW_USER_TABLE_COLUMNS;
  userTableData: IUserFields[] = [];
  deletedUserTableData: IUserFields[] = [];

  // UI state
  isLoading = false;
  isActiveUsersAccordianOpen = true;
  isDeletedUsersAccordionOpen = false;
  isAddUserDrawerOpen = false;

  private subscriptions = new Subscription();

  constructor(
    private notification: NotificationService,
    private registerService: RegisterService,
    private modal: NzModalService,
  ) {}

  ngOnInit(): void {
    console.log('in activeusers');
    if (this.companyId) {
      this.getUsers();
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  getUsers(): void {
    if (!this.companyId) {
      this.notification.error(
        COMMON_STRINGS.warningMessages.companyIdNotAvailable,
      );
      return;
    }

    this.isLoading = true;
    this.subscriptions.add(
      this.registerService
        .getAllUsers(this.companyId)
        .pipe(
          map((response: ApiResponsePageUserResponseDTO) => {
            if (response.success && response.data?.content) {
              return response.data.content.map((user: UserResponseDTO) => ({
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                contactNumber: user.contactNumber,
                roleId: user.roleId,
                profilePictureUrl: user.profilePictureUrl,
                isActive: user.isActive,
                fullName: `${user.firstName} ${user.lastName}`,
                roledisplaytext: user.roleDisplayText || 'N/A',
                companyId: user.companyId,
              }));
            }
            return [];
          }),
          catchError((error: HttpErrorResponse) => {
            console.error('Error fetching users:', error);
            this.notification.error('Failed to fetch users');
            return of([]);
          }),
        )
        .subscribe({
          next: (users: IUserFields[]) => {
            this.userTableData = users.filter((user) => user.isActive);
            this.deletedUserTableData = users.filter((user) => !user.isActive);
            this.isLoading = false;
            if (
              this.userTableData.some(
                (user) => user.companyId !== this.companyId,
              )
            ) {
              this.notification.warning(
                COMMON_STRINGS.warningMessages.userWarning,
              );
            }
          },
          error: () => {
            this.isLoading = false;
          },
        }),
    );
  }

  toggleActiveUsersAccordian(): void {
    this.isActiveUsersAccordianOpen = !this.isActiveUsersAccordianOpen;
  }

  toggleDeletedUsersAccordion(): void {
    this.isDeletedUsersAccordionOpen = !this.isDeletedUsersAccordionOpen;
  }

  openAddUserDrawer(): void {
    this.isAddUserDrawerOpen = true;
  }

  closeAddUserDrawer(): void {
    this.isAddUserDrawerOpen = false;
  }

  onUserAdded(newUser: IUserFields): void {
    // Add the new user to the beginning of the array (top of table)
    this.userTableData = [newUser, ...this.userTableData];

    // Close the drawer
    this.closeAddUserDrawer();

    // Refresh users to ensure data consistency
    this.getUsers();

    // Show success notification
    this.notification.success(
      `User "${newUser.firstName} ${newUser.lastName}" added successfully!`,
    );

    // Emit event to parent
    this.userUpdated.emit();
  }

  onTableDataClick(data: ITableDataClickOutput<IUserFields>): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'toggle':
        this.deleteUser(rowData);
        break;
    }
  }

  deleteUser(rowData: IUserFields): void {
    if (!rowData.id) {
      this.notification.error('User ID not found');
      return;
    }

    const isCurrentlyActive = rowData.isActive;
    const actionText = isCurrentlyActive ? 'deactivate' : 'activate';
    const userName = `${rowData.firstName} ${rowData.lastName}`.trim();
    const confirmMessage = COMMON_STRINGS.confirmMessages.toggleUserStatusConfirmation
      .replace('${actionText}', actionText)
      .replace('${userName}', userName);

    this.modal.confirm({
      nzTitle: confirmMessage,
      nzOkText: COMMON_STRINGS.dialogConfigurations.buttonLabels.ConfirmDelete,
      nzOkType: 'primary',
      nzOkDanger: true,
      nzCancelText: COMMON_STRINGS.dialogConfigurations.buttonLabels.Cancel,
      nzOnOk: () => {
        this.isLoading = true;
        this.registerService
          .activeInactiveUser(rowData.id, !isCurrentlyActive)
          .subscribe({
            next: (response) => {
              this.isLoading = false;
              if (response.success) {
                if (isCurrentlyActive) {
                  this.moveUserToInactive(rowData);
                } else {
                  this.moveUserToActive(rowData);
                }
                this.notification.success(`User ${actionText}d successfully`);
                this.userUpdated.emit();
              } else {
                this.notification.error(
                  `Failed to ${actionText} user: ${response.message || 'Unknown error'}`,
                );
              }
            },
            error: (error: HttpErrorResponse) => {
              this.isLoading = false;
              this.notification.error(
                `Failed to ${actionText} user: ${error.error?.message || error.message || 'Network error'}`,
              );
            },
          });
      },
    });
  }

  private moveUserToActive(rowData: IUserFields): void {
    this.deletedUserTableData = this.deletedUserTableData.filter(
      (user) => user.id !== rowData.id,
    );

    const userAlreadyExists = this.userTableData.some(
      (user) => user.id === rowData.id,
    );

    if (!userAlreadyExists) {
      this.userTableData = [
        ...this.userTableData,
        { ...rowData, isActive: true },
      ];
    }
  }

  private moveUserToInactive(rowData: IUserFields): void {
    this.userTableData = this.userTableData.filter(
      (user) => user.id !== rowData.id,
    );

    const userAlreadyExists = this.deletedUserTableData.some(
      (user) => user.id === rowData.id,
    );

    if (!userAlreadyExists) {
      this.deletedUserTableData = [
        ...this.deletedUserTableData,
        { ...rowData, isActive: false },
      ];
    }
  }
}
