.company-view-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.tabset {
    margin-top: 50px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
}

::ng-deep .ant-tabs-content-holder {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
}

::ng-deep .ant-tabs-tabpane {
    flex: 1;
    overflow-y: auto;
    margin-left: 4rem;
    padding-right: 16px;
    overflow-x: auto;
}

::ng-deep .company-view-tabs .ant-tabs-tab {
    font-size: 18px;
}

.company-details-container {
    padding-top: 15px;
}

.action-buttons {
    gap: 20px;
}

.field-label {
    color: grey;
}

.inactive-accordian {
    cursor: pointer;
    padding: 10px;
    border-radius: 4px;
    color: var(--primary-button-color);
}

.active-accordian {
    cursor: pointer;
    padding: 10px;
    border-radius: 4px;
    color: var(--primary-button-color);
    justify-content: space-between;
}

.chevron {
    margin-left: 8px;
    margin-top: 4px;
}



.user-edit-form-buttons {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.save-edit-button {
    height: 36px;
    width: 120px;
    background-color: var(--primary-button-color);
    border-radius: 7px;
    color: #FFFFFF;

}

.cancel-edit-button {
    height: 36px;
    width: 120px;
    background-color: var(--primary-button-color);
    border-radius: 7px;
    color: #FFFFFF;

}

::ng-deep .ant-drawer-title {
    font-size: 22px !important;
    font-weight: bold;
}

.save-edit-button:disabled {
    background-color: #d3d3d3;
    color: #a9a9a9;
    border-color: #d3d3d3;
    cursor: not-allowed;
}

.company-edit-form-fields {
    display: block;
}

.input-company-field {
    height: 45px;
    border-radius: 3px;
    border: 1px solid #d8cece;
}

.company-details-container .form-buttons {
    gap: 15px;
    display: flex;
}

.save-editCompany-button,
.cancel-editCompany-button {
    height: 40px;
}

nz-breadcrumb {
    padding-left: 4rem;
}

.active-breadcrumb {
    color: #1890ff; 
    cursor: pointer;
    font-weight: bold;
}

::ng-deep .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    position: relative;
    background-color: #fff;
    border: none;
    border-radius: none;
    transition: none;

}

::ng-deep nz-input-group.ant-input-affix-wrapper,
nz-select.ant-select .ant-select-selector {
    border-radius: none;
    box-shadow: none;
}

.billing-accordian {
    display: flex !important;
    justify-content: center;
    margin-top: 35px;
    color: var(--primary-button-color);
    margin-bottom: 35px;
}

.company-edit-form {
    margin-right: 0;
    margin-left: 0;
}
