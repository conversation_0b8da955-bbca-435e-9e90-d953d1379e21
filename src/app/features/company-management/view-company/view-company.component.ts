import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';

import { DataGridComponent } from '../../data-grid/data-grid.component';
import { ActiveInactiveUsersComponent } from '../active-inactive-users/active-inactive-users.component';

import {
  DUMMY_PRICING_DATA,
  DUMMY_PRICING_VIEW_DATA,
  PRICING_MANAGEMENT_COMPANY_LEVEL_TABLE_COLUMNS,
  VIEW_PRICING_DETAILS_COLUMNS,
} from '../../../core/tableColumns/pricing.column';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { ROUTES } from '../../../core/constants/routes';

import { ICompanyFields } from '../../../core/interface/company-fields';
import {
  IPricingFields,
  IPricingViewDetails,
} from '../../../core/interface/pricing-fields';
import { ITableDataClickOutput } from '../../../core/interface/table';

import { CompanyStateService } from '../../../core/services/company-state.service';
import { NotificationService } from '../../../core/services/notification.service';
import { CompanyDTO, AddressResponseDTO } from '../../../api-client';
import { EditCompanyFormComponent } from '../edit-company-form/edit-company-form.component';

@Component({
  selector: 'app-view-company',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzButtonModule,
    NzTabsModule,
    NzSwitchModule,
    NzFormModule,
    NzInputModule,
    ReactiveFormsModule,
    DataGridComponent,
    EditCompanyFormComponent,
    ActiveInactiveUsersComponent,
    FormsModule,
    NzDrawerModule,
    NzIconModule,
    NzBreadCrumbModule,
    NzSelectModule,
    NzCheckboxModule,
  ],
  templateUrl: './view-company.component.html',
  styleUrls: ['./view-company.component.scss', '../../../../styles.scss'],
})
export class ViewCompanyComponent implements OnInit {
  companyId?: number | null = null;
  company: ICompanyFields | null = null;
  isEditFormVisible = false;
  isLoading = false;
  pricingManagementCompanyLevelTableColumns =
    PRICING_MANAGEMENT_COMPANY_LEVEL_TABLE_COLUMNS;
  pricingManagementTableData: IPricingFields[] = DUMMY_PRICING_DATA;
  openPricingView = false;
  viewPricingTableColumns = VIEW_PRICING_DETAILS_COLUMNS;
  viewPricingTableData: IPricingViewDetails[] = DUMMY_PRICING_VIEW_DATA;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private companyStateService: CompanyStateService,
    private notification: NotificationService,
    private cdr: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    const id = this.route.snapshot.paramMap.get('id');
    this.companyId = id ? Number(id) : null;
    if (this.companyId) {
      this.company = this.companyStateService.getCompanyData();
      console.log('🔍 Loaded company data:', this.company);
    } else {
      this.router.navigate([ROUTES.sidebar.company]);
    }
  }

  get primaryAddressFormatted(): string {
    if (!this.company?.primaryAddress) return 'N/A';
    return this.formatAddress(this.company.primaryAddress);
  }

  get billingAddressFormatted(): string {
    if (!this.company?.billingAddress) return 'N/A';
    return this.formatAddress(this.company.billingAddress);
  }

  private formatAddress(address: AddressResponseDTO): string {
    if (!address) return 'N/A';
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.suburb,
      address.stateName,
      address.zipCode,
    ].filter((part) => part);
    return parts.join(', ') || 'N/A';
  }

  goBack() {
    this.companyStateService.clearCompanyData();
    this.router.navigate([ROUTES.sidebar.company]);
  }

  editCompany() {
    this.isEditFormVisible = true;
  }

  toggleActive(): void {
    if (!this.company) return;

    this.companyStateService.toggleCompanyStatus(
      this.company,
      () => {
        this.notification.success(
          `Company status updated to ${this.company?.isActive ? 'active' : 'inactive'}`,
        );
        this.cdr.detectChanges();
      },
      (errorMessage) => {
        this.notification.error(
          COMMON_STRINGS.warningMessages.companyStatusUpdateFailure.replace(
            '${errorMessage}',
            errorMessage,
          ),
        );
      },
    );
  }

  onCompanySaved(updatedCompany: CompanyDTO): void {
    console.log('🔍 Company saved:', updatedCompany);
    // Update the local company data with the response from the server
    this.company = {
      ...this.company,
      ...updatedCompany,
      primaryAddress: {
        ...updatedCompany.primaryAddress,
        addressType: AddressResponseDTO.AddressTypeEnum.Primary,
        stateName: updatedCompany.primaryAddress?.stateName,
        zipCode: updatedCompany.primaryAddress?.zipCode,
      },
      billingAddress: {
        ...updatedCompany.billingAddress,
        addressType: AddressResponseDTO.AddressTypeEnum.Billing,
        stateName: updatedCompany.billingAddress?.stateName,
        zipCode: updatedCompany.billingAddress?.zipCode,
      },
    };
    this.companyStateService.setCompanyData(this.company);
    this.isEditFormVisible = false;
    this.cdr.detectChanges();
    console.log('✅ Updated company in ViewCompanyComponent:', this.company);
  }

  onFormCancelled(): void {
    this.isEditFormVisible = false;
    this.companyStateService.clearTempCompanyData();
    this.cdr.detectChanges();
  }

  onUserUpdated(): void {
    console.log('Users updated in ActiveInactiveUsersComponent');
  }

  onPricingManagementTableDataClick(
    data: ITableDataClickOutput<IPricingFields>,
  ): void {
    const { actionField } = data;
    switch (actionField) {
      case 'view':
        this.viewPricingDetails();
        break;
    }
  }

  viewPricingDetails(): void {
    this.openPricingView = true;
  }

  onClosePricingDetails() {
    this.openPricingView = false;
  }
}
