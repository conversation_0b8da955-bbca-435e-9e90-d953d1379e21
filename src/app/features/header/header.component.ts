import { Component, OnInit } from '@angular/core';
import { TitleService } from '../../core/services/title.service';
import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-header',
  imports: [],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
})
export class HeaderComponent implements OnInit, OnDestroy {
  pageTitle = '';
  private titleSubscription!: Subscription;

  constructor(private titleService: TitleService) {}

  ngOnInit(): void {
    this.titleSubscription = this.titleService.title$.subscribe((title) => {
      this.pageTitle = title;
    });
  }

  ngOnDestroy(): void {
    this.titleSubscription?.unsubscribe();
  }
}
