.layout-wrapper {
  height: 100vh;
  width: 100vw;
  overflow: hidden;


  &.with-sidebar {
    padding-left: var(--sidebar-expanded-width);
    transition: padding-left 0.3s;
    &.collapsed {
      padding-left: var(--sidebar-collapsed-width);
    }
  }
}

.main-content {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;

  &.with-sidebar {
    transition: margin-left 0.3s;
  }
}

.header {
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 64px;
  flex-shrink: 0;
}

.header.light-theme {
  background-color: #368eed;
  color: white;
}

.ant-menu,
.ant-menu-horizontal>.ant-menu-item a {
  color: white;
}

.header.dark-theme {
  background-color: #368eed;
  color: white;
}

.header-left {
  display: flex;
  align-items: center;

  .header-logo {
    height: 32px;
  }
}

.content {
  overflow: auto;
  background-color: #ffffff;
}

.content::-webkit-scrollbar {
    width: 5px; 
}

.content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.5); 
    border-radius: 5px;
}

.content::-webkit-scrollbar-track {
    background-color: rgba(240, 240, 240, 1); 
    border-radius: 5px; 
}



.ant-menu {
  box-sizing: none;
}

.nav-menu {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  margin-left: auto;
  background-color: #368eed;
  border: none;
}

.logo-text {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1.2;

}

a {
  text-decoration: none;
}

.active {
  font-weight: bold;
}

.menu-icon {
  font-size: 16px;
  margin-right: 4px;
}

.active-link {
  a {
    font-weight: bold;
    color: White;
  }
}


.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-item-active::after,
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-item-open::after,
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-item-selected::after,
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-item:hover::after,
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-submenu-active::after,
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-submenu-open::after,
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-submenu-selected::after,
.ant-menu-horizontal:not(.ant-menu-dark)>.ant-menu-submenu:hover::after {
  border-bottom: 0 !important;
}

.ant-menu-horizontal>.ant-menu-item::after,
.ant-menu-horizontal>.ant-menu-submenu::after {
  border-bottom: 0 !important;
}

.nav-menu a:hover {
  color: #ffd700; 
  text-decoration: underline;
  transition: color 0.3s;
}


.footer {
  text-align: center;
    flex-shrink: 0;
    padding: 15px 20px;
    border-top: 1px solid #e8e8e8;
    height: 64px;
    background: #F1F1F1;


  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .footer-copyright {
    font-size: 12px;
    color: #595959;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
  }

  .footer-address {
    white-space: nowrap;
  }

  .footer-divider {
    margin: 0 5px;
  }

  .footer-links {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .footer-link {
    color: #adb9c5;
    text-decoration: none;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }

  .footer-social {
    display: flex;
    gap: 15px;

    a {
      font-size: 16px;
      transition: color 0.3s;
    }
  }
}

.social-icons {
  display: flex;
  gap: 0;
  a {
    font-size: 15px;
    color: var(--footer-icon-color); 
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s;
  }
}

.location-icon{
  color: var(--footer-icon-color); 
}

@media (max-width: 500px) {
  .header {
    padding: 0 10px;
    height: 56px;
  }
  .header-left {
    padding: 0 10px;
  }
  .header-logo {
    height: 24px;
  }
  .header-logo img {
    height: 24px;
  }
  .header-logo-text {
    font-size: 16px;
  }
  .header-menu {
    font-size: 14px;
  }

}

.logo-container {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  padding: 12px 13px;
  gap: 10px;
  .logo {
    height: 32px;
    margin-right: 10px;
    transition: margin 0.3s ease;
  }

  .logo-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--logo-text-color);
    transition: opacity 0.2s ease;
  }
}

.icon {
  position: relative;
  width: 24px;
  height: 24px;
}

.badge {
  position: absolute;
  top: -5px;
  right: -8px;
  background: red;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 10px;
}

.content {
  overflow:auto;
}

.content::-webkit-scrollbar {
    width: 5px; 
}

.content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.5); 
    border-radius: 5px;
}

.content::-webkit-scrollbar-track {
    background-color: rgba(240, 240, 240, 1); 
    border-radius: 5px; 
}

