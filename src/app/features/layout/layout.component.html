<div class="layout-wrapper" [class.with-sidebar]="showSidebar" [ngClass]="{'collapsed': isSidebarCollapsed}">
  <!-- Sidebar -->
  <app-sidebar
    *ngIf="showSidebar"
    [(isSidebarCollapsed)]="isSidebarCollapsed"
    [username]="username"
    [email]="email"
    [isLoggedIn]="isUserLoggedIn"
  ></app-sidebar>

  <nz-layout class="main-content" [class.with-sidebar]="showSidebar">
    
    <!-- Top bar -->
    <app-top-bar [showHeader]="showHeader" [profileImageUrl]="profileImageUrl" [username]="username"></app-top-bar>

    <!-- Routed content -->
    <nz-content class="content">
      <app-header></app-header>
      <router-outlet></router-outlet>
    </nz-content>

    <!-- Footer -->
    <nz-footer class="footer" *ngIf="showFooter">
      <div class="footer-content">
        <div class="footer-left">
          <span class="footer-address">
            <i class="fa-solid fa-location-dot location-icon"></i> 123 Service Lane, Sydney
          </span>
        </div>
        <div class="footer-center">
          <span>© {{currentYear}} Areadocs. All Rights Reserved.</span>
        </div>
        <div class="footer-right">
          <div class="social-icons">
            <a href="https://facebook.com" data-testid="facebook" target="_blank" aria-label="Facebook"><i class="fa-brands fa-facebook"></i></a>
            <a href="https://twitter.com" data-testid="twitter" target="_blank" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
            <a href="https://instagram.com" data-testid="instagram" target="_blank" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="https://linkedin.com" data-testid="linkedin" target="_blank" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
          </div>
        </div>
      </div>
    </nz-footer>
  </nz-layout>
</div>
