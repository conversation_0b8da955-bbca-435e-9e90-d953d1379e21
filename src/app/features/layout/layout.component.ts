import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { RouterModule, RouterOutlet } from '@angular/router';

import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule, NzMenuThemeType } from 'ng-zorro-antd/menu';
import { NzIconModule, NZ_ICONS } from 'ng-zorro-antd/icon';
import { NzBadgeModule } from 'ng-zorro-antd/badge';

import {
  UserOutline,
  LoginOutline,
  ApartmentOutline,
} from '@ant-design/icons-angular/icons';

import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

import { SidebarComponent } from '../sidebar/sidebar.component';

import { INavItem } from '../../core/interface/nav-item';

import { UserService } from '../../core/services/user.service';

import { TopBarComponent } from '../top-bar/top-bar.component';

import { HeaderComponent } from '../header/header.component';

@Component({
  selector: 'app-layout',
  imports: [
    NzLayoutModule,
    RouterOutlet,
    CommonModule,
    RouterModule,
    NzMenuModule,
    NzIconModule,
    SidebarComponent,
    FontAwesomeModule,
    NzBadgeModule,
    TopBarComponent,
    HeaderComponent,
  ],
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss',
  providers: [
    {
      provide: NZ_ICONS,
      useValue: [UserOutline, LoginOutline, ApartmentOutline], // Register required icons
    },
  ],
})
export class LayoutComponent {
  @Input() navItems: INavItem[] = [];
  @Input() showFooter = true;
  @Input() isUserLoggedIn = false;
  @Input() theme: NzMenuThemeType = 'light';
  @Input() username = '';
  @Input() email = '';
  @Input() showSidebar = true;
  @Input() showHeader = true;
  @Input() profileImageUrl: string | null = null;
  isSidebarCollapsed = window.innerWidth < 1000;
  constructor(private userService: UserService) {}

  get userRoleId() {
    return this.userService.userInfo?.roleId ?? 0;
  }

  get currentYear() {
    return new Date().getFullYear();
  }
}
