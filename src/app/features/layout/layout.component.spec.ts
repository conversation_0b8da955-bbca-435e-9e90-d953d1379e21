// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { LayoutComponent } from './layout.component';
// import { RouterTestingModule } from '@angular/router/testing';
// import { By } from '@angular/platform-browser';
// import { NZ_ICONS, NzIconModule } from 'ng-zorro-antd/icon';
// import { HttpClientModule } from '@angular/common/http';
// import { ApartmentOutline, LoginOutline, UserOutline } from '@ant-design/icons-angular/icons';

// describe('LayoutComponent', () => {
//   let component: LayoutComponent;
//   let fixture: ComponentFixture<LayoutComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [
//         LayoutComponent,
//         RouterTestingModule,
//         NzIconModule,
//         HttpClientModule,
//       ],
//       providers: [
//         {
//           provide: NZ_ICONS,
//           useValue: [ApartmentOutline, UserOutline, LoginOutline],
//         }
//       ]
//     }).compileComponents();

//     fixture = TestBed.createComponent(LayoutComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create the layout component', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should render the logo with icon', () => {
//     const compiled = fixture.nativeElement as HTMLElement;
//     expect(compiled.querySelector('.logo')?.textContent).toContain('Areadocs');
//     expect(compiled.querySelector('nz-icon')).toBeTruthy();
//   });

//   it('should show footer if showFooter is true', () => {
//     component.showFooter = true;
//     fixture.detectChanges();
//     const footer = fixture.debugElement.query(By.css('nz-footer'));
//     expect(footer).toBeTruthy();
//   });

//   it('should not show footer if showFooter is false', () => {
//     component.showFooter = false;
//     fixture.detectChanges();
//     const footer = fixture.debugElement.query(By.css('nz-footer'));
//     expect(footer).toBeNull();
//   });

//   it('should render nav menu items if navItems are provided', () => {
//     component.navItems = [
//       { label: 'Home', route: '/home', showIcon: true, iconName: 'user', requiresAuth: false },
//       { label: 'Login', route: '/login', showIcon: true, iconName: 'login', requiresAuth: false }
//     ];
//     fixture.detectChanges();
//     const items = fixture.debugElement.queryAll(By.css('.nav-menu li'));
//     expect(items.length).toBe(2);
//   });

//   it('should apply correct theme class to header', () => {
//     component.theme = 'dark';
//     fixture.detectChanges();
//     const header = fixture.debugElement.query(By.css('.header'));
//     expect(header.nativeElement.classList).toContain('dark-theme');

//     component.theme = 'light';
//     fixture.detectChanges();
//     expect(header.nativeElement.classList).toContain('light-theme');
//   });
// });
