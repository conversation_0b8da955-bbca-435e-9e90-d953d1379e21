.page-container {
  display: flex;
  flex-direction: column;
}

.top-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.search-row {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 950px;
  margin-bottom: 2rem;
}

.search-container {
  width: 100%;
  max-width: 1000px;
}


.title-reference {
  font-size: 1rem;
  margin-bottom: 4px;
}

.input-with-icon {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-input {
  flex: 1;
}

.search-icon-clickable {
  cursor: pointer;
  color: #000;
  font-size: 24px;
  color: var(--primary-button-color);

  &:hover {
    color: #40a9ff;
  }
}

.info-icon-wrapper {

  border-radius: 100%;
  background-color: #195FAC;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

nz-icon {
  font-size: 16px;
  line-height: 1;
}

.sample-input-text {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}