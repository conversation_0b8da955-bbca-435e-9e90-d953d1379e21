import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectOptionInterface } from 'ng-zorro-antd/select';

import { AddressSearchComponent } from '../address-search/address-search.component';
import { PlanSearchComponent } from '../plan-search/plan-search.component';

import { EnumSearchType } from '../../../core/enumerations/search-type';

import { PropertyDTO } from '../../../api-client';
import { MapSearchComponent } from '../map-search/map-search.component';

@Component({
  selector: 'app-property-search',
  imports: [
    FormsModule,
    NzRadioModule,
    AddressSearchComponent,
    PlanSearchComponent,
    MapSearchComponent,
  ],
  templateUrl: './property-search.component.html',
  styleUrls: ['./property-search.component.scss', '../search-types.scss'],
})
export class PropertySearchComponent {
  enumSearchTypes = EnumSearchType;

  currentSearchType = EnumSearchType.Address;

  selectedPfiID: number | null = null;
  isLoading = false;
  options: NzSelectOptionInterface[] = [];
  allPropertyResults: PropertyDTO[] = [];
  showRadio = true;
}
