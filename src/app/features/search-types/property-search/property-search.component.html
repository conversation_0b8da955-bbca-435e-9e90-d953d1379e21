@if(showRadio){
  <nz-radio-group [(ngModel)]="currentSearchType" class="mb-3">  
    <label nz-radio [nzValue]="enumSearchTypes.Address">{{enumSearchTypes.Address}}</label>
    <label nz-radio [nzValue]="enumSearchTypes.Plan">{{enumSearchTypes.Plan}}</label>
    <label nz-radio [nzValue]="enumSearchTypes.Map">{{enumSearchTypes.Map}}</label>
  </nz-radio-group>
}


@switch (currentSearchType) {
  @case(enumSearchTypes.Address) {
    <app-address-search [(showRadio)]="showRadio"></app-address-search>
  }
  @case (enumSearchTypes.Plan) {
    <app-plan-search></app-plan-search>
  }
  @case (enumSearchTypes.Map) {
    <app-map-search></app-map-search>
  }
}
