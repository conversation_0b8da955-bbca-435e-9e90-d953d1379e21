// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { MapSearchComponent } from './map-search.component';
// import { By } from '@angular/platform-browser';

// describe('MapSearchComponent', () => {
//   let component: MapSearchComponent;
//   let fixture: ComponentFixture<MapSearchComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [MapSearchComponent],
//     }).compileComponents();
//   });

//   beforeEach(() => {
//     fixture = TestBed.createComponent(MapSearchComponent);
//     component = fixture.componentInstance;

//     // Mock data for testing
//     component.mapMarkers = [
//       { id: 1, name: 'Marker 1', coordinates: { lat: -37.8136, lng: 144.9631 } },
//       { id: 2, name: 'Marker 2', coordinates: { lat: -33.8688, lng: 151.2093 } },
//     ];

//     fixture.detectChanges();
//   });

//   it('should create the MapSearchComponent', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should render the map container', () => {
//     const mapContainer = fixture.debugElement.query(By.css('.map-container')).nativeElement;
//     expect(mapContainer).toBeTruthy();
//   });

//   it('should render the correct number of map markers', () => {
//     const markerElements = fixture.debugElement.queryAll(By.css('.map-marker'));
//     expect(markerElements.length).toBe(component.mapMarkers.length);
//   });

//   it('should display the correct marker details', () => {
//     const markerElements = fixture.debugElement.queryAll(By.css('.map-marker'));
//     markerElements.forEach((marker, index) => {
//       expect(marker.nativeElement.textContent).toContain(component.mapMarkers[index].name);
//     });
//   });

//   it('should emit an event when a marker is clicked', () => {
//     spyOn(component.markerSelected, 'emit');
//     const markerElement = fixture.debugElement.query(By.css('.map-marker'));
//     markerElement.triggerEventHandler('click', null);
//     expect(component.markerSelected.emit).toHaveBeenCalledWith(component.mapMarkers[0]);
//   });
// });
