import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ISearchData } from '../../../core/interface/search-base.interface';
import { MapViewComponent } from '../../shared/map-search/map-view.component';
import { BehaviorSubject } from 'rxjs';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-map-search',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MapViewComponent,
    NzInputModule,
    NzIconModule,
  ],
  templateUrl: './map-search.component.html',
  styleUrls: ['./map-search.component.scss'],
})
export class MapSearchComponent implements OnInit {
  searchResults: ISearchData[] = [];
  searchQuery = '';
  search$ = new BehaviorSubject<void>(undefined);

  ngOnInit(): void {
    // Simulate loading data
    this.searchResults = [];
  }

  search(): void {
    // Implement map search logic
    console.log('Searching on map:', this.searchQuery);
    this.search$.next();
  }
}
