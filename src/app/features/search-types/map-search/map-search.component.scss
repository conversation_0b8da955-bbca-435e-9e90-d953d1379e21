.search-input-container {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

.search-input-group {
  display: flex;
  flex: 1;
}

.search-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #d9d9d9;
  border-right: none;
  outline: none;
  font-size: 14px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.search-btn {
  background-color: #1890ff;
  border: none;
  color: white;
  padding: 0 20px;
  cursor: pointer;
  transition: background-color 0.3s;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  
  &:hover {
    background-color: #40a9ff;
  }
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-dropdown {
  .dropdown-btn {
    background-color: white;
    border: 1px solid #d9d9d9;
    padding: 8px 15px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    
    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }
}

.map-container {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.map-view {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f0f2f5;
}

.map-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.map-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.action-btn {
  background-color: white;
  border: 1px solid #d9d9d9;
  padding: 5px 15px;
  border-radius: 4px;
  cursor: pointer;
  
  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }
}
