<div class="search-input-container">
  <nz-input-group [nzSuffix]="suffixIconSearch">
    <input type="text" nz-input placeholder="Enter location to search on map..." [(ngModel)]="searchQuery"
      (keyup.enter)="search()" data-testid="volume-folio-input" />
  </nz-input-group>
  
  <ng-template #suffixIconSearch>
    <i nz-icon nzType="search" (click)="search()" data-testid="volume-search-button"></i>
  </ng-template>

  <div class="search-filters">
    <div class="filter-dropdown">
      <button class="dropdown-btn">Polygon <i class="fa fa-chevron-down"></i></button>
    </div>
    <div class="filter-dropdown">
      <button class="dropdown-btn">Radius <i class="fa fa-chevron-down"></i></button>
    </div>
  </div>
</div>

<div class="map-container">
  <div class="map-view">
    <app-map-view [(searchText)]="searchQuery" [showSearchBox]="false" [search$]="search$"></app-map-view>
  </div>
</div>
