// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { PlanSearchComponent } from './plan-search.component';
// import { By } from '@angular/platform-browser';

// describe('PlanSearchComponent', () => {
//   let component: PlanSearchComponent;
//   let fixture: ComponentFixture<PlanSearchComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [PlanSearchComponent],
//     }).compileComponents();
//   });

//   beforeEach(() => {
//     fixture = TestBed.createComponent(PlanSearchComponent);
//     component = fixture.componentInstance;

// //     // Mock data for testing
//     component.plans = [
//       { id: 1, name: 'Plan A', description: 'Description for Plan A', price: 100 },
//       { id: 2, name: 'Plan B', description: 'Description for Plan B', price: 200 },
//     ];

//     fixture.detectChanges();
//   });

//   it('should create the PlanSearchComponent', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should render the correct number of plans', () => {
//     const planElements = fixture.debugElement.queryAll(By.css('.plan-item'));
//     expect(planElements.length).toBe(component.plans.length);
//   });

//   it('should display the correct plan details', () => {
//     const planElements = fixture.debugElement.queryAll(By.css('.plan-item'));
//     planElements.forEach((plan, index) => {
//       expect(plan.nativeElement.textContent).toContain(component.plans[index].name);
//       expect(plan.nativeElement.textContent).toContain(component.plans[index].description);
//       expect(plan.nativeElement.textContent).toContain(component.plans[index].price);
//     });
//   });

//   it('should emit an event when a plan is selected', () => {
//     spyOn(component.planSelected, 'emit');
//     const planElement = fixture.debugElement.query(By.css('.plan-item'));
//     planElement.triggerEventHandler('click', null);
//     expect(component.planSelected.emit).toHaveBeenCalledWith(component.plans[0]);
//   });
// });
