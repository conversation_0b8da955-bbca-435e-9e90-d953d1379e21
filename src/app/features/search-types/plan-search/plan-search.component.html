<div class="page-container">
  <!-- Top Section -->
  <div class="top-section">
    <div class="search-row">
      <!-- Input Group + Icon in same row -->
      <div class="search-container">
        <!-- Labels -->
        <div class="plan-search">Plan Search</div>
       

        <!-- Input row: input and icon inline -->
        <nz-select 
          nzPlaceHolder="Enter Lot-Plan Number..." 
          [nzShowSearch]="true" 
          [nzServerSearch]="true"
          [nzAllowClear]="true"
          [nzShowArrow]="true" 
          [nzSuffixIcon]="suffixIconSearch"
          [nzLoading]="isLoading" 
          [(ngModel)]="selectedPfiID" 
          (nzOnSearch)="onSearch($event)" 
          data-testid="plansearch-selectbox"
          >
          @if (!isLoading) {
            @for (option of parcelOptions; track option) {
              <nz-option [nzValue]="option" [nzLabel]="option.label + ''"></nz-option>
            }
          } @else {
            <nz-option nzDisabled nzCustomContent>
              <nz-icon nzType="loading" class="loading-icon" />
              Loading Data...
            </nz-option>
          }
        </nz-select>

          <ng-template #suffixIconSearch>
            <i
              nz-icon
              nzType="search"
              class="search-icon-clickable"
              data-testid="volume-search-button"
            ></i>
          </ng-template>

        <!-- Sample text -->
        <div class="sample-input-text">
          Sample input:   3A2\PS515587
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div *ngIf="selectedPfiID" class="data-grid-container">
      <app-product-search
        [propertyPfi]="selectedPfiID.value"
      ></app-product-search>
    </div>
  </div>
</div>
