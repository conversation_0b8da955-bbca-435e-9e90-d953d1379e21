import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import {
  NzOptionComponent,
  NzSelectModule,
  NzSelectOptionInterface,
} from 'ng-zorro-antd/select';
import { SearchService } from '../../../core/services/search.service';

import { NotificationService } from '../../../core/services/notification.service';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { SEARCH_TABLE_COLUMNS } from '../../../core/tableColumns/search.column';
import { IParcelProperty } from '../../../core/interface/parcel-property.interface';
import { ProductSearchComponent } from '../product-search/product-search.component';
import { BehaviorSubject, debounceTime } from 'rxjs';

@Component({
  selector: 'app-plan-search',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzInputModule,
    NzIconModule,
    NzSelectModule,
    NzOptionComponent,
    ProductSearchComponent,
  ],
  templateUrl: './plan-search.component.html',
  styleUrls: ['./plan-search.component.scss', '../search-types.scss'],
})
export class PlanSearchComponent implements OnInit {
  parcelProperties: IParcelProperty[] = [];
  parcelOptions: NzSelectOptionInterface[] = [];
  isSearchPerformed = false; // Flag to indicate if a search has been performed
  isLoading = false; // Loading state for the search
  tableColumns = SEARCH_TABLE_COLUMNS;
  selectedPfiID?: NzSelectOptionInterface;
  searchChange$ = new BehaviorSubject('');

  constructor(
    private searchService: SearchService,
    private notificationService: NotificationService,
  ) {}

  ngOnInit(): void {
    this.searchChange$.pipe(debounceTime(500)).subscribe({
      next: (planNumber) => this.getParcelProperties(planNumber),
    });
  }

  private getParcelProperties(planNumber: string): void {
    if (!planNumber?.trim()) {
      return;
    }
    this.isLoading = true;
    this.isSearchPerformed = true;

    this.searchService.searchPlan(planNumber).subscribe({
      next: (results) => this.handlePlanSearchResults(results),
      error: (error: HttpErrorResponse) => this.handlePlanSearchError(error),
    });
  }

  private handlePlanSearchResults(results: IParcelProperty[]): void {
    this.parcelProperties = results;
    this.parcelOptions = results
      .map((result) => result.propertyPfiOptions)
      .filter((x): x is NzSelectOptionInterface => !!x);
    this.isLoading = false;
  }

  private handlePlanSearchError(error: HttpErrorResponse): void {
    console.error('Search error:', error);
    this.isSearchPerformed = false;
    this.isLoading = false;
  }

  private search(searchText: string): void {
    if (!searchText) {
      this.notificationService.warningMessage(
        COMMON_STRINGS.warningMessages.addressSearchWarning,
      );
      return;
    }
    this.searchChange$.next(searchText);
  }

  onSearch(searchText: string | null) {
    if (searchText && searchText.trim().length > 3) {
      this.search(searchText.trim());
    } else {
      this.isLoading = false;
      this.parcelOptions = [];
    }
  }
}
