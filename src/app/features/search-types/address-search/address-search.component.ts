import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BehaviorSubject, debounceTime } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import {
  NzOptionComponent,
  NzSelectModule,
  NzSelectOptionInterface,
} from 'ng-zorro-antd/select';

import { HttpErrorResponse } from '@angular/common/http';

import { SearchService } from '../../../core/services/search.service';
import { NotificationService } from '../../../core/services/notification.service';

import { ISearchData } from '../../../core/interface/search-base.interface';

import { SEARCH_TABLE_COLUMNS } from '../../../core/tableColumns/search.column';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { ProductSearchComponent } from '../product-search/product-search.component';
import { IPropertySearch } from '../../../core/interface/property-search.interface';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { NzSplitterModule } from 'ng-zorro-antd/splitter';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { PropertyDetailsDrawerComponent } from '../property-details-drawer/property-details-drawer.component';

@Component({
  selector: 'app-address-search',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzIconModule,
    NzSelectModule,
    NzOptionComponent,
    NzButtonModule,
    ProductSearchComponent,
    NzFlexModule,
    NzSplitterModule,
    NzDrawerModule,
    PropertyDetailsDrawerComponent,
  ],
  templateUrl: './address-search.component.html',
  styleUrls: ['./address-search.component.scss', '../search-types.scss'],
})
export class AddressSearchComponent implements OnInit {
  @Input() showRadio = true;
  @Output() showRadioChange = new EventEmitter<boolean>();

  visible = false;

  selectedPfiID?: NzSelectOptionInterface;
  searchResults: ISearchData[] = [];
  propertySummery: IPropertySearch[] = [];
  selectedProperty?: IPropertySearch;
  isLoading = false;
  tableColumns = SEARCH_TABLE_COLUMNS;
  options: NzSelectOptionInterface[] = [];
  searchChange$ = new BehaviorSubject('');
  currentIndex = 0;
  splitSizes: (string | number)[] = ['70%', '30%'];

  onResize(sizes: (string | number)[]) {
    this.splitSizes = sizes;
  }

  constructor(
    private searchService: SearchService,
    private notificationService: NotificationService,
  ) {}

  ngOnInit(): void {
    this.searchChange$.pipe(debounceTime(500)).subscribe({
      next: (propertyPfi) => this.getProperties(propertyPfi),
    });
  }
  private handleAddressSearchResults(results: IPropertySearch[]): void {
    this.propertySummery = results || [];
    this.options = results
      .map((result) => result.addressOptions)
      .filter((option) => !!option);
    this.isLoading = false;
  }

  private handleAddressSearchError(error: HttpErrorResponse) {
    console.error('Search error:', error);
    this.notificationService.errorMessage(
      COMMON_STRINGS.errorMessages.failedToFetchPropertyDetails,
    );
    this.isLoading = false;
  }

  private search(searchText: string): void {
    if (!searchText) {
      return;
    }
    this.searchChange$.next(searchText);
  }

  private getProperties(eziAddress: string) {
    if (!eziAddress?.trim()) {
      return;
    }
    this.isLoading = true;
    this.searchService.searchAddress({ eziAddress }).subscribe({
      next: (response) => this.handleAddressSearchResults(response),
      error: (error) => this.handleAddressSearchError(error),
    });
  }

  onSearch(searchText: string | null) {
    if (searchText && searchText.trim().length > 3) {
      this.search(searchText.trim());
    } else {
      this.isLoading = false;
      this.options = [];
    }
  }

  onClickPrevious() {
    this.currentIndex--;
    if (this.currentIndex == 0) {
      this.showRadioChange.emit(true);
    }
  }
  onClickNext() {
    this.currentIndex++;
    if (this.currentIndex == 1) {
      this.showRadioChange.emit(false);
    }
  }

  onChange() {
    this.selectedProperty = this.propertySummery.find(
      (property) => property.propertyPfi === this.selectedPfiID?.value,
    );
  }

  get splitterLayOut() {
    return window.innerWidth >= 1000 ? 'horizontal' : 'vertical';
  }

  open() {
    this.visible = true;
  }

  close() {
    this.visible = false;
  }
}
