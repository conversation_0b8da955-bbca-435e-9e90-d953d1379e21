import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { AddressSearchComponent } from './address-search.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzFlexModule } from 'ng-zorro-antd/flex';
import { NzSplitterModule } from 'ng-zorro-antd/splitter';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { of, throwError } from 'rxjs';
import { SearchService } from '../../../core/services/search.service';
import { NotificationService } from '../../../core/services/notification.service';
import { ProductSearchComponent } from '../product-search/product-search.component';
import { PropertyDetailsDrawerComponent } from '../property-details-drawer/property-details-drawer.component';
import { HttpErrorResponse } from '@angular/common/http';
import { By } from '@angular/platform-browser';
import { NzSelectComponent } from 'ng-zorro-antd/select';
import { IPropertySearch } from '../../../core/interface/property-search.interface';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Component, Input } from '@angular/core';

// Mock ProductSearchComponent
@Component({
  selector: 'app-product-search',
  template: '<div>Mock Product Search</div>',
  standalone: true,
})
class MockProductSearchComponent {
  @Input() propertyPfi?: number;
}

// Mock PropertyDetailsDrawerComponent
@Component({
  selector: 'app-property-details-drawer',
  template: '<div>Mock Property Details Drawer</div>',
  standalone: true,
})
class MockPropertyDetailsDrawerComponent {
  @Input() selectedProperty?: IPropertySearch;
}

describe('AddressSearchComponent', () => {
  let component: AddressSearchComponent;
  let fixture: ComponentFixture<AddressSearchComponent>;
  let searchService: jasmine.SpyObj<SearchService>;
  let notificationService: jasmine.SpyObj<NotificationService>;

  // Mock data
  const mockProperties: IPropertySearch[] = [
    {
      propertyPfi: 123,
      primaryAddress: { eziAddress: '328/800 SWANSTON STREET CARLTON 3053' },
      addressOptions: {
        value: 123,
        key: 123,
        label: '328/800 SWANSTON STREET CARLTON 3053',
      },
    },
    {
      propertyPfi: 456,
      primaryAddress: { eziAddress: '123 COLLINS STREET MELBOURNE 3000' },
      addressOptions: {
        value: 456,
        key: 456,
        label: '123 COLLINS STREET MELBOURNE 3000',
      },
    },
  ];

  beforeEach(async () => {
    // Create spies for services
    searchService = jasmine.createSpyObj('SearchService', [
      'searchAddress',
      'getProducts',
    ]);
    notificationService = jasmine.createSpyObj('NotificationService', [
      'errorMessage',
      'warningMessage',
    ]);

    await TestBed.configureTestingModule({
      imports: [
        AddressSearchComponent, // Add this back
        FormsModule,
        CommonModule,
        NzIconModule,
        NzButtonModule,
        NzSelectModule,
        NzFlexModule,
        NzSplitterModule,
        NzDrawerModule,
        HttpClientTestingModule,
        MockProductSearchComponent,
        MockPropertyDetailsDrawerComponent,
      ],
      providers: [
        { provide: SearchService, useValue: searchService },
        { provide: NotificationService, useValue: notificationService },
        provideNoopAnimations(),
      ],
    })
      .overrideComponent(AddressSearchComponent, {
        remove: {
          imports: [ProductSearchComponent, PropertyDetailsDrawerComponent],
        },
        add: {
          imports: [
            MockProductSearchComponent,
            MockPropertyDetailsDrawerComponent,
          ],
        },
      })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddressSearchComponent);
    component = fixture.componentInstance;

    // Don't trigger change detection immediately to avoid ngOnInit issues
    // fixture.detectChanges();
  });

  // Test case: Component creation
  describe('Component Creation', () => {
    it('should create the AddressSearchComponent', () => {
      expect(component).toBeTruthy();
      fixture.detectChanges(); // Trigger change detection here where needed
    });
  });

  // Test case: Initial state
  describe('Initial State', () => {
    it('should initialize with default values', () => {
      fixture.detectChanges();
      expect(component.showRadio).toBeTrue();
      expect(component.visible).toBeFalse();
      expect(component.isLoading).toBeFalse();
      expect(component.searchResults).toEqual([]);
      expect(component.propertySummery).toEqual([]);
      expect(component.options).toEqual([]);
      expect(component.currentIndex).toBe(0);
      expect(component.splitSizes).toEqual(['70%', '30%']);
    });
  });

  // Test case: Search Functionality
  describe('Search Functionality', () => {
    it('should call searchService and update options on valid search input', fakeAsync(() => {
      searchService.searchAddress.and.returnValue(of(mockProperties));

      // Initialize component to set up the BehaviorSubject subscription
      fixture.detectChanges();

      // Prime the BehaviorSubject with 2 values to get past skip(2)
      component.searchChange$.next('first');
      component.searchChange$.next('second');

      // Trigger the search through onSearch method
      component.onSearch('328/800 SWANSTON STREET CARLTON 3053');
      tick(600); // Wait for debounce + a bit extra

      expect(searchService.searchAddress).toHaveBeenCalledWith({
        eziAddress: '328/800 SWANSTON STREET CARLTON 3053',
      });
      expect(component.isLoading).toBeFalse();
      expect(component.propertySummery).toEqual(mockProperties);
      expect(component.options).toEqual(
        mockProperties.map((p) => p.addressOptions).filter((x) => !!x),
      );
    }));

    it('should show warning and not call searchService for short input', () => {
      component.onSearch('12');
      expect(notificationService.warningMessage).not.toHaveBeenCalled(); // Component doesn't warn for short input, just ignores it
      expect(searchService.searchAddress).not.toHaveBeenCalled();
      expect(component.isLoading).toBeFalse();
      expect(component.options).toEqual([]);
    });

    it('should show warning and not call searchService for empty input', () => {
      component.onSearch('');
      expect(notificationService.warningMessage).not.toHaveBeenCalled(); // Component doesn't warn for empty input, just ignores it
      expect(searchService.searchAddress).not.toHaveBeenCalled();
      expect(component.isLoading).toBeFalse();
      expect(component.options).toEqual([]);
    });

    it('should handle search error and show notification', fakeAsync(() => {
      const error = new HttpErrorResponse({ error: 'Server error' });
      searchService.searchAddress.and.returnValue(throwError(() => error));

      // Initialize component to set up the BehaviorSubject subscription
      fixture.detectChanges();

      // Prime the BehaviorSubject with 2 values to get past skip(2)
      component.searchChange$.next('first');
      component.searchChange$.next('second');

      // Now call onSearch which will trigger the actual search
      component.onSearch('328/800 SWANSTON STREET CARLTON 3053');
      tick(600); // Wait for debounce + a bit extra

      expect(searchService.searchAddress).toHaveBeenCalledWith({
        eziAddress: '328/800 SWANSTON STREET CARLTON 3053',
      });
      expect(notificationService.errorMessage).toHaveBeenCalledWith(
        COMMON_STRINGS.errorMessages.failedToFetchPropertyDetails,
      );
      expect(component.isLoading).toBeFalse();
    }));

    it('should trigger search when typing in nz-select search box', fakeAsync(() => {
      fixture.detectChanges(); // Initialize component first
      searchService.searchAddress.and.returnValue(of(mockProperties));

      // Prime the BehaviorSubject with 2 values to get past skip(2)
      component.searchChange$.next('first');
      component.searchChange$.next('second');

      // Trigger through the select component's nzOnSearch event
      const selectElement = fixture.debugElement.query(
        By.directive(NzSelectComponent),
      ).componentInstance;
      selectElement.nzOnSearch.emit('328/800 SWANSTON STREET CARLTON 3053');
      tick(600); // Wait for debounce

      expect(searchService.searchAddress).toHaveBeenCalledWith({
        eziAddress: '328/800 SWANSTON STREET CARLTON 3053',
      });
      expect(component.options).toEqual(
        mockProperties.map((p) => p.addressOptions).filter((x) => !!x),
      );
    }));
  });

  // Test case: Property Selection
  describe('Property Selection', () => {
    it('should update selectedProperty on nzSelect change', () => {
      component.propertySummery = mockProperties;
      component.selectedPfiID = {
        value: 123,
        key: 123,
        label: '328/800 SWANSTON STREET CARLTON 3053',
      };
      component.onChange();

      expect(component.selectedProperty).toEqual(mockProperties[0]);
    });

    it('should display selected property address when selectedPfiID is set', () => {
      component.selectedPfiID = {
        value: 123,
        key: 123,
        label: '328/800 SWANSTON STREET CARLTON 3053',
      };
      component.selectedProperty = mockProperties[0];
      fixture.detectChanges();

      const addressElement = fixture.debugElement.query(By.css('p'));
      expect(addressElement.nativeElement.textContent).toContain(
        '328/800 SWANSTON STREET CARLTON 3053',
      );
    });
  });

  // Test case: Navigation
  describe('Navigation', () => {
    it('should increment currentIndex and emit showRadioChange(false) on next click', () => {
      spyOn(component.showRadioChange, 'emit');
      component.onClickNext();

      expect(component.currentIndex).toBe(1);
      expect(component.showRadioChange.emit).toHaveBeenCalledWith(false);
    });

    it('should decrement currentIndex and emit showRadioChange(true) when currentIndex becomes 0', () => {
      component.currentIndex = 1;
      spyOn(component.showRadioChange, 'emit');
      component.onClickPrevious();

      expect(component.currentIndex).toBe(0);
      expect(component.showRadioChange.emit).toHaveBeenCalledWith(true);
    });
  });

  // Test case: Drawer Functionality
  describe('Drawer Functionality', () => {
    it('should toggle drawer visibility', () => {
      component.open();
      expect(component.visible).toBeTrue();

      component.close();
      expect(component.visible).toBeFalse();
    });

    it('should open drawer when View Details button is clicked', () => {
      component.selectedPfiID = {
        value: 123,
        key: 123,
        label: '328/800 SWANSTON STREET CARLTON 3053',
      };
      fixture.detectChanges();

      const viewDetailsButton = fixture.debugElement.query(
        By.css('[data-testid="view-property-details-btn"]'),
      );
      viewDetailsButton.triggerEventHandler('click', null);

      expect(component.visible).toBeTrue();
    });

    it('should close drawer when close button is clicked', () => {
      component.visible = true;
      fixture.detectChanges();

      const closeButton = fixture.debugElement.query(
        By.css('[data-testid="property-details-drawer-close-btn"]'),
      );
      closeButton.triggerEventHandler('click', null);

      expect(component.visible).toBeFalse();
    });
  });

  // Test case: UI Layout
  describe('UI Layout', () => {
    it('should return horizontal splitter layout for wide screens', () => {
      spyOnProperty(window, 'innerWidth').and.returnValue(1200);
      expect(component.splitterLayOut).toBe('horizontal');
    });

    it('should return vertical splitter layout for narrow screens', () => {
      spyOnProperty(window, 'innerWidth').and.returnValue(800);
      expect(component.splitterLayOut).toBe('vertical');
    });

    it('should update splitSizes on resize', () => {
      const newSizes = ['60%', '40%'];
      component.onResize(newSizes);
      expect(component.splitSizes).toEqual(newSizes);
    });
  });
});
