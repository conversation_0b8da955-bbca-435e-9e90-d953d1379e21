<div class="page-container">
  <!-- Top Section -->
  <div class="top-section">
    <div class="search-row">
      <div class="search-container">
        <div class="address-search">Address Search</div>


        <nz-select nzPlaceHolder="Enter Address..." [nzShowSearch]="true" [nzServerSearch]="true" [nzAllowClear]="true"
        [nzLoading]="isLoading" [(ngModel)]="selectedPfiID" (ngModelChange)="onChange()" data-testid="addresssearch-selectbox"
          (nzOnSearch)="onSearch($event)" [nzSuffixIcon]="suffixIconSearch">
          @if (!isLoading) {
            @for (option of options; track option) {
              <nz-option [nzValue]="option" [nzLabel]="option.label + ''"></nz-option>
            }
          } @else {
            <nz-option nzDisabled nzCustomContent>
              <nz-icon nzType="loading" class="loading-icon" />
              Loading Data...
            </nz-option>
          }
        </nz-select>

        <ng-template #suffixIconSearch>
            <i
              nz-icon
              nzType="search"
              class="search-icon-clickable"
              data-testid="volume-search-button"
            ></i>
          </ng-template>

        <div class="sample-input-text">
          Sample input: 328/800 SWANSTON STREET CARLTON 3053
        </div>
      </div>
    </div>
    <!-- Search Results -->
    <div class="data-grid-container" *ngIf="selectedPfiID">
      <button style="float: right;" class="me-4 mb-2" nz-button nzType="primary" nzGhost (click)="open()" data-testid="view-property-details-btn">View Details</button>
      @if (selectedProperty?.primaryAddress?.eziAddress) {
        <p>{{selectedProperty?.primaryAddress?.eziAddress}}</p>
      }
      <app-product-search [propertyPfi]="selectedPfiID.value"></app-product-search>
    </div>
  </div>
</div>


<!-- Property details Drawer Section-->
<nz-drawer [nzClosable]="false" [nzVisible]="visible" nzPlacement="right" nzSize="large" [nzTitle]="drawerTitle" [nzExtra]="extra"
  (nzOnClose)="close()">
  <ng-container *nzDrawerContent>
    <app-property-details-drawer [selectedProperty]="selectedProperty"></app-property-details-drawer>
  </ng-container>
</nz-drawer>

<!-- Property Details Header  -->
<ng-template #drawerTitle>
  <h2>Property Details</h2>
</ng-template>

<!-- Extra close button inside drawer  -->
<ng-template #extra>
  <button nz-button nzType="primary" nzGhost (click)="close()" data-testid="property-details-drawer-close-btn">
    <nz-icon nzType="close-circle" nzTheme="fill" />
    Close
  </button>
</ng-template>
