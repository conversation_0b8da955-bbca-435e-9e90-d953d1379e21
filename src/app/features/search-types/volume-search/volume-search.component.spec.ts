// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { FormsModule } from '@angular/forms';
// import { VolumeSearchComponent } from './volume-search.component';
//
// describe('VolumeSearchComponent', () => {
//   let component: VolumeSearchComponent;
//   let fixture: ComponentFixture<VolumeSearchComponent>;
//
//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [FormsModule],
//       declarations: [VolumeSearchComponent],
//     }).compileComponents();
//
//     fixture = TestBed.createComponent(VolumeSearchComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });
//
//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
//
//   it('should initialize searchResults as an empty array', () => {
//     expect(component.searchResults).toEqual([]);
//   });
//
//   it('should log search parameters when search is called', () => {
//     const consoleSpy = spyOn(console, 'log');
//     component.volumeNumber = '123';
//     component.folioNumber = '456';
//     component.search();
//     expect(consoleSpy).toHaveBeenCalledWith(
//       'Searching for volume:',
//       '123',
//       'folio:',
//       '456'
//     );
//   });
// });
