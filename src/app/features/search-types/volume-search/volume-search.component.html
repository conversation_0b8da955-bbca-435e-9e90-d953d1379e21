<div class="page-container">
  <!-- Top Section -->
  <div class="top-section">
    <div class="search-row">
      <!-- Input Group + Icon in same row -->
      <div class="search-container">
        <!-- Labels -->
        <div class="Volume-search">Title Search</div>
       

        <!-- Input row: input and icon inline -->
        <div class="input-with-icon">
          <nz-input-group [nzSuffix]="suffixIconSearch">
            <input
              type="text"
              nz-input
              placeholder="Enter Volume Folio..."
              [(ngModel)]="titleId"
              (ngModelChange)="onChange()"
              (keyup.enter)="search()"
              class="search-input"
              
              data-testid="volume-folio-input"
            />
          </nz-input-group>

          <ng-template #suffixIconSearch>
            <i
              nz-icon
              nzType="search"
              class="search-icon-clickable"
              (click)="search()"
              data-testid="volume-search-button"
            ></i>
          </ng-template>

          <!-- Info Icon beside input -->
        
        </div>

        <!-- Sample text -->
        <div class="sample-input-text">
          Sample input: 10940/843
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div *ngIf="isSearchPerformed && titleId" class="data-grid-container">
      <app-product-search
        [titleId]="titleId"
      ></app-product-search>
    </div>
  </div>
</div>
