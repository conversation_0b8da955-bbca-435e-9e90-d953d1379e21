import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';

import { ProductSearchComponent } from '../product-search/product-search.component';

@Component({
  selector: 'app-volume-search',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    ProductSearchComponent,
    NzInputModule,
    NzIconModule,
  ],
  templateUrl: './volume-search.component.html',
  styleUrls: ['./volume-search.component.scss', '../search-types.scss'],
})
export class VolumeSearchComponent {
  titleId = '';
  isSearchPerformed = false;
  search(): void {
    this.isSearchPerformed = true;
  }

  onChange() {
    this.isSearchPerformed = false;
  }
}
