import { CommonModule } from '@angular/common';
import { Component, inject, Input, OnInit } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';

import { DataGridComponent } from '../../data-grid/data-grid.component';

import { SearchService } from '../../../core/services/search.service';
import { NotificationService } from '../../../core/services/notification.service';

import { ISearchData } from '../../../core/interface/search-base.interface';

import { COMMON_STRINGS } from '../../../core/constants/common';
import { SEARCH_TABLE_COLUMNS } from '../../../core/tableColumns/search.column';
import { ITableDataClickOutput } from '../../../core/interface/table';

import { CartService } from '../../../core/services/cart.service';

import { SharedService } from '../../../core/services/shared.service';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { CartRequestDTO } from '../../../api-client';

@Component({
  selector: 'app-product-search',
  imports: [
    CommonModule,
    DataGridComponent,
    NzDrawerModule,
    NzIconModule,
    NzTabsModule,
    NzCheckboxModule,
    NzButtonModule,
    FormsModule,
  ],
  templateUrl: './product-search.component.html',
  styleUrl: './product-search.component.scss',
})
export class ProductSearchComponent implements OnInit {
  @Input() titleId?: string;
  @Input() propertyPfi?: string;
  @Input() documentId?: string;

  @Input() isVisible = true;

  drawerVisible = false;
  selectedTabIndex = 0;
  tableColumns = SEARCH_TABLE_COLUMNS;
  isLoading = false;
  searchResults: ISearchData[] = [];
  termsAndConditionMessage = '';
  currentSearchData?: ISearchData;

  acceptedTermsAndConditions = false;

  private notificationService = inject(NotificationService);
  private searchService = inject(SearchService);
  private cartService = inject(CartService);
  private sharedService = inject(SharedService);

  private getProducts(
    titleId?: string,
    propertyPfi?: string,
    documentId?: string,
  ) {
    this.isLoading = true;
    this.searchService
      .getProducts({ titleId, propertyPfi, documentId })
      .subscribe({
        next: (response) => this.handleSearchResults(response),
        error: (error) => this.handleSearchError(error),
      });
  }

  private handleSearchResults(results: ISearchData[]): void {
    this.searchResults = results || [];
    this.isLoading = false;
  }

  private handleSearchError(error: HttpErrorResponse): void {
    console.error('Search error:', error);
    this.notificationService.errorMessage(
      COMMON_STRINGS.errorMessages.failedToFetchProductDetails,
    );
    this.searchResults = [];
    this.isLoading = false;
  }

  ngOnInit(): void {
    if (!this.titleId && !this.propertyPfi && !this.documentId) {
      this.notificationService.error('Please provide an input');
    } else {
      this.setBtnLabel();
      this.getProducts(this.titleId, this.propertyPfi, this.documentId);
    }
  }
  onTableDataClick = (data: ITableDataClickOutput<ISearchData>) => {
    const { rowData, actionField } = data;
    switch (actionField) {
      case 'addToCart': {
        const isInCart = this.sharedService.cartItems.some(
          (item) => item.productCode === rowData.productCode,
        );
        if (!isInCart) {
          this.termsAndConditionMessage = rowData.customerWarning ?? '';
          this.currentSearchData = rowData;
          this.drawerVisible = true;
        } else {
          this.removeItem(rowData);
        }
        break;
      }

      default:
        this.notificationService.error(
          COMMON_STRINGS.warningMessages.addToCartWarning,
        );

        break;
    }
  };

  addToCart(rowData?: ISearchData) {
    if (rowData) {
      const cartData: CartRequestDTO = {
        folderId: 1,
        productCode: rowData.productCode,
        volumeFolio: '',
        isWarningAcknowledged: true,
      };

      this.cartService.addToCart([cartData]).subscribe({
        next: (response) => {
          rowData.productId = response.items?.[0]?.id;
          this.getCart();
          this.currentSearchData = undefined;
          this.close();
        },
        error: (error) => {
          console.error('Cart error:', error);
          this.close();
        },
      });
    }
  }

  removeItem(rowData: ISearchData): void {
    const cartItemId =
      rowData.productId ||
      this.sharedService.cartItems.find(
        (item) => item.productCode === rowData.productCode,
      )?.id;
    if (cartItemId) {
      this.cartService.removeFromCart(cartItemId).subscribe({
        next: () => {
          this.getCart();
          rowData.productId = undefined;
          this.notificationService.successMessage(
            COMMON_STRINGS.successMessages.removeFromCartSuccess,
          );
        },
        error: (err) => {
          console.error('Failed to remove item:', err);
          this.notificationService.error(`Failed to remove item`);
        },
      });
    }
  }

  getCart() {
    this.cartService.getCart().subscribe({
      next: (response) => {
        this.sharedService.cartItems = response.items || [];
      },
      error: (error) => {
        console.error('Cart error:', error);
      },
    });
  }

  setBtnLabel(): void {
    const actionColumn = SEARCH_TABLE_COLUMNS.find(
      (col) => col.field === 'action',
    );
    if (!actionColumn?.actionFieldColumns) return;

    const addToCartColumn = actionColumn.actionFieldColumns.find(
      (col) => col.field === 'addToCart',
    );
    if (!addToCartColumn) return;

    addToCartColumn.svgIconPathFn = (rowData) => {
      const isInCart = this.sharedService.cartItems.some(
        (item) => item.productCode === rowData.productCode,
      );
      return isInCart
        ? 'assets/svg/added-to-cart.svg'
        : 'assets/svg/add-to-cart.svg';
    };

    addToCartColumn.showBadgeOverIconFn = (rowData) => {
      const isInCart = this.sharedService.cartItems.some(
        (item) => item.productCode === rowData.productCode,
      );
      return isInCart;
    };
  }

  close() {
    this.termsAndConditionMessage = '';
    this.acceptedTermsAndConditions = false;
    this.drawerVisible = false;
  }
}
