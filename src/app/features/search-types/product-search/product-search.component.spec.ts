import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpErrorResponse } from '@angular/common/http';
import { By } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { of, throwError } from 'rxjs';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzNotificationModule } from 'ng-zorro-antd/notification';

import { SearchService } from '../../../core/services/search.service';
import { NotificationService } from '../../../core/services/notification.service';
import { CartService } from '../../../core/services/cart.service';
import { SharedService } from '../../../core/services/shared.service';
import { DataGridComponent } from '../../data-grid/data-grid.component';
import { ProductSearchComponent } from './product-search.component';
import { ISearchData } from '../../../core/interface/search-base.interface';
import { ITableDataClickOutput } from '../../../core/interface/table';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { SEARCH_TABLE_COLUMNS } from '../../../core/tableColumns/search.column';

import { CartItemDTO } from '../../../api-client';

describe('ProductSearchComponent', () => {
  let component: ProductSearchComponent;
  let fixture: ComponentFixture<ProductSearchComponent>;
  let searchService: jasmine.SpyObj<SearchService>;
  let notificationService: jasmine.SpyObj<NotificationService>;
  let cartService: jasmine.SpyObj<CartService>;
  let sharedService: jasmine.SpyObj<SharedService>;

  beforeEach(async () => {
    // Mock services
    searchService = jasmine.createSpyObj<SearchService>('SearchService', [
      'getProducts',
    ]);
    notificationService = jasmine.createSpyObj<NotificationService>(
      'NotificationService',
      ['error', 'errorMessage', 'successMessage'],
    );
    cartService = jasmine.createSpyObj<CartService>('CartService', [
      'addToCart',
      'removeFromCart',
      'getCart',
    ]);
    // let cartItems: CartItemDTO[] = [];
    sharedService = {
      cartItems: [] as CartItemDTO[],
    } as SharedService;
    await TestBed.configureTestingModule({
      imports: [
        ProductSearchComponent,
        HttpClientTestingModule,
        CommonModule,
        FormsModule,
        NzDrawerModule,
        NzIconModule,
        NzTabsModule,
        NzCheckboxModule,
        NzButtonModule,
        NzNotificationModule,
        DataGridComponent,
      ],
      providers: [
        { provide: SearchService, useValue: searchService },
        { provide: NotificationService, useValue: notificationService },
        { provide: CartService, useValue: cartService },
        { provide: SharedService, useValue: sharedService },
        provideNoopAnimations(), // Fix NG05105 error
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ProductSearchComponent);
    component = fixture.componentInstance;

    // Mock default service responses
    searchService.getProducts.and.returnValue(of([]));
    cartService.getCart.and.returnValue(of({ items: [] }));
    cartService.addToCart.and.returnValue(of({ items: [{ id: 1 }] }));
    cartService.removeFromCart.and.returnValue(of({}));
  });

  describe('Component Creation', () => {
    it('should create', () => {
      fixture.detectChanges();
      expect(component).toBeTruthy();
    });
  });

  describe('Lifecycle Methods', () => {
    it('should show error notification if no inputs provided', () => {
      component.titleId = undefined;
      component.propertyPfi = undefined;
      component.documentId = undefined;
      fixture.detectChanges(); // Trigger ngOnInit
      expect(notificationService.error).toHaveBeenCalledWith(
        'Please provide an input',
      );
      expect(searchService.getProducts).not.toHaveBeenCalled();
    });

    it('should fetch products and set button label if inputs provided', () => {
      component.titleId = '123';
      spyOn(component, 'setBtnLabel');
      fixture.detectChanges(); // Trigger ngOnInit
      expect(searchService.getProducts).toHaveBeenCalledWith({
        titleId: '123',
        propertyPfi: undefined,
        documentId: undefined,
      });
      expect(component.setBtnLabel).toHaveBeenCalled();
    });
  });

  describe('Input Properties', () => {
    it('should hide data grid when isVisible is false', () => {
      component.isVisible = false;
      fixture.detectChanges();
      const dataGrid = fixture.debugElement.query(
        By.directive(DataGridComponent),
      );
      expect(dataGrid).toBeNull();
    });

    it('should render data grid when isVisible is true', () => {
      component.isVisible = true;
      fixture.detectChanges();
      const dataGrid = fixture.debugElement.query(
        By.directive(DataGridComponent),
      );
      expect(dataGrid).toBeTruthy();
    });

    it('should pass titleId to getProducts', () => {
      component.titleId = '456';
      fixture.detectChanges();
      expect(searchService.getProducts).toHaveBeenCalledWith({
        titleId: '456',
        propertyPfi: undefined,
        documentId: undefined,
      });
    });
  });

  describe('Service Interactions', () => {
    it('should update searchResults on successful product fetch', fakeAsync(() => {
      const mockResults: ISearchData[] = [
        { productCode: 'P1', productDescription: 'Product 1' },
        { productCode: 'P2', productDescription: 'Product 2' },
      ];
      searchService.getProducts.and.returnValue(of(mockResults));
      component.titleId = '123';
      fixture.detectChanges();
      tick();
      console.log(sharedService.cartItems);

      expect(component.searchResults).toEqual(mockResults);
      expect(component.isLoading).toBeFalse();
    }));

    it('should show error notification on product fetch error', fakeAsync(() => {
      searchService.getProducts.and.returnValue(
        throwError(() => new HttpErrorResponse({ status: 500 })),
      );
      component.titleId = '123';
      fixture.detectChanges();
      tick();
      expect(notificationService.errorMessage).toHaveBeenCalledWith(
        COMMON_STRINGS.errorMessages.failedToFetchProductDetails,
      );
      expect(component.searchResults).toEqual([]);
      expect(component.isLoading).toBeFalse();
    }));

    it('should add item to cart successfully', fakeAsync(() => {
      const rowData: ISearchData = { productCode: 'P1' };
      cartService.addToCart.and.returnValue(of({ items: [{ id: 1 }] }));
      spyOn(component, 'getCart');
      component.addToCart(rowData);
      tick();
      expect(cartService.addToCart).toHaveBeenCalledWith([
        {
          folderId: 1,
          productCode: 'P1',
          volumeFolio: '',
          isWarningAcknowledged: true,
        },
      ]);
      expect(component.getCart).toHaveBeenCalled();
      expect(component.currentSearchData).toBeUndefined();
      expect(component.drawerVisible).toBeFalse();
    }));

    it('should remove item from cart successfully', fakeAsync(() => {
      const rowData: ISearchData = {
        productCode: 'P1',
        productDescription: 'Product 1',
        productId: 1,
      };
      cartService.removeFromCart.and.returnValue(of({}));
      component.removeItem(rowData);
      tick();
      expect(cartService.removeFromCart).toHaveBeenCalledWith(1);
      expect(notificationService.successMessage).toHaveBeenCalledWith(
        COMMON_STRINGS.successMessages.removeFromCartSuccess,
      );
      expect(rowData.productId).toBeUndefined();
    }));
  });

  describe('Template Rendering', () => {
    it('should render data grid with search results', () => {
      component.searchResults = [
        { productCode: 'P1', productDescription: 'Product 1' },
      ];
      component.isVisible = true;
      fixture.detectChanges();
      const dataGrid = fixture.debugElement.query(
        By.directive(DataGridComponent),
      );
      expect(dataGrid.componentInstance.tableData).toEqual(
        component.searchResults,
      );
      expect(dataGrid.componentInstance.loading).toBeFalse();
      expect(dataGrid.componentInstance.tableColumns).toEqual(
        SEARCH_TABLE_COLUMNS,
      );
    });

    it('should not render drawer when drawerVisible is false', () => {
      component.drawerVisible = false;
      fixture.detectChanges();
      const drawer = fixture.debugElement.query(By.css('nz-drawer'));
      expect(drawer).toBeTruthy('Drawer should exist in DOM');
      expect(drawer.componentInstance.nzVisible).toBeFalse();
    });

    it('should render drawer when drawerVisible is true', fakeAsync(() => {
      component.drawerVisible = true;
      fixture.detectChanges();
      tick(300); // Wait for drawer animation
      fixture.detectChanges();
      const drawer = fixture.debugElement.query(By.css('nz-drawer'));
      expect(drawer).toBeTruthy();
      expect(drawer.componentInstance.nzVisible).toBeTrue();
    }));

    it('should display terms and conditions message in drawer', fakeAsync(() => {
      component.drawerVisible = true;
      component.termsAndConditionMessage = 'Sample T&C';
      fixture.detectChanges();
      tick(300);
      fixture.detectChanges();
      const message = fixture.debugElement.query(
        By.css('.message-container p'),
      );
      expect(message).toBeTruthy();
      expect(message.nativeElement.textContent.trim()).toBe('Sample T&C');
    }));

    it('should disable agree button when acceptedTermsAndConditions is false', fakeAsync(() => {
      component.drawerVisible = true;
      component.acceptedTermsAndConditions = false;
      fixture.detectChanges();
      tick();
      fixture.detectChanges();
      const agreeButton = fixture.debugElement.query(
        By.css('button[nz-button][type="submit"]'),
      );
      expect(agreeButton.componentInstance.disabled).toBeTrue();
    }));
  });

  describe('Event Handling', () => {
    it('should open drawer on addToCart table click if not in cart', () => {
      const rowData: ISearchData = {
        productCode: 'P1',
        productDescription: 'Product 1',
        customerWarning: 'Warning',
      };
      const tableData: ITableDataClickOutput<ISearchData> = {
        rowData,
        actionField: 'addToCart',
        rowIndex: 0,
      };
      sharedService.cartItems = [];
      component.onTableDataClick(tableData);
      expect(component.drawerVisible).toBeTrue();
      expect(component.termsAndConditionMessage).toBe('Warning');
      expect(component.currentSearchData).toEqual(rowData);
    });

    it('should remove item on addToCart click if already in cart', () => {
      const rowData: ISearchData = {
        productCode: 'P1',
        productDescription: 'Product 1',
        productId: 1,
      };
      const tableData: ITableDataClickOutput<ISearchData> = {
        rowData,
        actionField: 'addToCart',
        rowIndex: 0,
      };
      sharedService.cartItems = [{ productCode: 'P1' }];
      expect(sharedService.cartItems).toEqual([{ productCode: 'P1' }]);
      spyOn(component, 'removeItem');
      component.onTableDataClick(tableData);
      expect(component.removeItem).toHaveBeenCalledWith(rowData);
    });

    it('should show warning for invalid action field', () => {
      const rowData: ISearchData = {
        productCode: 'P1',
        productDescription: 'Product 1',
      };
      const tableData: ITableDataClickOutput<ISearchData> = {
        rowData,
        actionField: 'invalid',
        rowIndex: 0,
      };
      component.onTableDataClick(tableData);
      expect(notificationService.error).toHaveBeenCalledWith(
        COMMON_STRINGS.warningMessages.addToCartWarning,
      );
    });

    it('should submit form and add to cart when terms are accepted', fakeAsync(() => {
      component.drawerVisible = true;
      component.acceptedTermsAndConditions = true;
      component.currentSearchData = {
        productCode: 'P1',
        productDescription: 'Product 1',
      };
      spyOn(component, 'addToCart');
      fixture.detectChanges();
      tick();
      const form = fixture.debugElement.query(By.css('form'));
      form.triggerEventHandler('ngSubmit', null);
      expect(component.addToCart).toHaveBeenCalledWith(
        component.currentSearchData,
      );
    }));

    it('should close drawer on cancel button click', fakeAsync(() => {
      component.drawerVisible = true;
      component.termsAndConditionMessage = 'Test T&C';
      component.acceptedTermsAndConditions = true;
      fixture.detectChanges();
      tick();
      const cancelButton = fixture.debugElement.query(
        By.css('button[nz-button][nzGhost]'),
      );
      cancelButton.triggerEventHandler('click', null);
      expect(component.drawerVisible).toBeFalse();
      expect(component.termsAndConditionMessage).toBe('');
      expect(component.acceptedTermsAndConditions).toBeFalse();
    }));

    it('should close drawer when nzOnClose emits', fakeAsync(() => {
      component.drawerVisible = true;
      fixture.detectChanges();
      tick(300);
      const drawer = fixture.debugElement.query(By.css('nz-drawer'));
      drawer.triggerEventHandler('nzOnClose', null);
      fixture.detectChanges();
      expect(component.drawerVisible).toBeFalse();
      expect(drawer.componentInstance.nzVisible).toBeFalse();
    }));
  });

  describe('Drawer Content', () => {
    it('should display drawer title', fakeAsync(() => {
      component.drawerVisible = true;
      fixture.detectChanges();
      tick(300);
      const title = fixture.debugElement.query(By.css('.ant-drawer-title'));
      expect(title).toBeTruthy();
    }));

    it('should display Terms & Conditions title in drawer', fakeAsync(() => {
      component.drawerVisible = true;
      component.currentSearchData = { productCode: 'P1' }; // Ensure content renders
      fixture.detectChanges();
      tick(300);
      fixture.detectChanges();
      // Try alternative selectors: title in content or nzTitle
      let title = fixture.debugElement.query(By.css('nz-drawer h2'));
      if (!title) {
        title = fixture.debugElement.query(
          By.css('nz-drawer .ant-drawer-title h2'),
        );
        if (!title) {
          title = fixture.debugElement.query(By.css('.ant-drawer-title')); // NG-ZORRO title class
        }
      }
      expect(title).toBeTruthy('Drawer title should exist');
      expect(title.nativeElement.textContent.trim()).toContain(
        'Terms & Conditions',
      );
    }));

    it('should display Terms & Conditions title in drawer', fakeAsync(() => {
      component.drawerVisible = true;
      component.currentSearchData = { productCode: 'P1' };
      fixture.detectChanges();
      tick(300);
      fixture.detectChanges();
      let title = fixture.debugElement.query(By.css('nz-drawer h2'));
      if (!title) {
        title = fixture.debugElement.query(
          By.css('nz-drawer .ant-drawer-title h2'),
        );
        if (!title) {
          title = fixture.debugElement.query(By.css('.ant-drawer-title'));
        }
      }
      expect(title).toBeTruthy('Drawer title should exist');
      expect(title.nativeElement.textContent.trim()).toContain(
        'Terms & Condition',
      );
    }));
  });
});
