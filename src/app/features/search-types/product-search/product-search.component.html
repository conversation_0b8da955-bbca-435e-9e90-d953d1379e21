@if(isVisible) {
<app-data-grid [tableData]="searchResults" [loading]="isLoading" [tableColumns]="tableColumns" [showPagination]="false"
  [frontPagination]="false" [scrollX]="'100%'" [scrollY]="null" (tableDataClick)="onTableDataClick($event)"></app-data-grid>
}

<nz-drawer [nzClosable]="false" [nzVisible]="drawerVisible" nzPlacement="right" nzSize="large" [nzTitle]="drawerTitle"
  [nzExtra]="extra" (nzOnClose)="close()">
  <ng-container *nzDrawerContent>
    <nz-tabset [(nzSelectedIndex)]="selectedTabIndex">
      <nz-tab nzTitle="Terms of Service">
        <form (ngSubmit)="addToCart(currentSearchData)">
          @if (termsAndConditionMessage) {
            <div class="message-container">
              <p>{{termsAndConditionMessage}}</p>
            </div>
          }
          <label nz-checkbox [(ngModel)]="acceptedTermsAndConditions" name="TnC">
            I have read the terms of servoce and privacy policy outlined
          </label>
          <div class="d-flex gap-2 mt-4">
            <button type="submit" nz-button nzType="primary" [disabled]="!acceptedTermsAndConditions">Agree & Accept</button>
            <button nz-button nzType="primary" nzGhost (click)="close()">Cancel</button>
          </div>
        </form>
      </nz-tab>
 
    </nz-tabset>
    <!-- <app-property-details-drawer [selectedProperty]="selectedProperty"></app-property-details-drawer> -->
  </ng-container>
</nz-drawer>

<ng-template #drawerTitle>
  <h2>Terms & Conditions</h2>
</ng-template>

<!-- Extra close button inside drawer  -->
<ng-template #extra>
  <button nz-button nzType="primary" nzGhost (click)="close()">
    <nz-icon nzType="close-circle" nzTheme="fill" />
    Close
  </button>
</ng-template>
