<div class="page-container">
  <!-- Top Section -->
  <div class="top-section">
    <div class="map-container">
      <div class="details-box">
        @if(selectedProperty) {
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-4">
                <h4>Primary Address</h4>
              </div>
              <div class="col-md-8">
                <p class="text-dark-gray">{{ selectedProperty.primaryAddress?.eziAddress }}</p>
              </div>
            </div>
          </div>
          <app-map-view [searchText]="selectedProperty.primaryAddress?.eziAddress ?? ''" [height]="400"></app-map-view>
          <h4 class="mt-4 text-dark-blue">Property Details</h4>
          <p><strong class="text-dark-blue">Property PFI:</strong> {{ selectedProperty.propertyPfi }}</p>
          <p><strong class="text-dark-blue">Status:</strong> {{ selectedProperty.propertyStatus }}</p>
          <p><strong class="text-dark-blue">Title Type:</strong> {{ selectedProperty.titles?.[0]?.titleType }}</p>
          <p><strong class="text-dark-blue">Parcel:</strong> {{ selectedProperty.landParcels?.[0]?.spi }}</p>
          <h5 class="mt-4 text-dark-gray">Secondary Address</h5>
          <p>{{ selectedProperty.aliasAddresses?.[0]?.eziAddress }}</p>
          <p>
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.buildingName }},
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.unitType }}
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.unitNumber }},
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.streetNumber }}
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.streetName }}
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.streetType }}
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.streetSuffix }},
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.suburbTownLocality }}
            {{ selectedProperty.aliasAddresses?.[0]?.addressDetails?.postcode }}
          </p>
        }
      </div>

    </div>

  </div>
</div>
