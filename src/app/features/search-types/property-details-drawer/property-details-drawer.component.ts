import { Component, Input } from '@angular/core';
import { IPropertySearch } from '../../../core/interface/property-search.interface';
import { MapViewComponent } from '../../shared/map-search/map-view.component';

@Component({
  selector: 'app-property-details-drawer',
  imports: [MapViewComponent],
  templateUrl: './property-details-drawer.component.html',
  styleUrl: './property-details-drawer.component.scss',
})
export class PropertyDetailsDrawerComponent {
  @Input() selectedProperty?: IPropertySearch;
}
