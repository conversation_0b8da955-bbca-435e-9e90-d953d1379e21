import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PropertyDetailsDrawerComponent } from './property-details-drawer.component';
import { MapViewComponent } from '../../shared/map-search/map-view.component';
import { By } from '@angular/platform-browser';
import { IPropertySearch } from '../../../core/interface/property-search.interface';
import { PropertyDTO, LandParcelDTO, TitleDTO } from '../../../api-client';

// Mock MapViewComponent to avoid testing its internal logic
// @Component({
//   selector: 'app-map-view',
//   template: '<div></div>',
//   standalone: true
// })
// class MockMapViewComponent {
//   searchText: string = '';
//   height: number = 400;
// }

// Base mock property data aligned with DTOs
const baseMockProperty: IPropertySearch = {
  primaryAddress: { eziAddress: '123 Main St, Springfield, 62701' },
  propertyPfi: 123,
  propertyStatus: PropertyDTO.PropertyStatusEnum.Active,
  isCouncilPropertyRegistered: false,
  isMultiAssess: false,
  propertyCreatedDate: '2023-01-01',
  aliasAddresses: [],
  landParcels: [
    {
      spi: 'Parcel123',
      parcelType: LandParcelDTO.ParcelTypeEnum.Lot,
      lotType: LandParcelDTO.LotTypeEnum.Unrestricted,
      parcelStatus: LandParcelDTO.ParcelStatusEnum.Active,
    },
  ],
  titles: [
    {
      titleType: TitleDTO.TitleTypeEnum.Freehold,
      titleStatus: TitleDTO.TitleStatusEnum.Active,
    },
  ],
};

describe('PropertyDetailsDrawerComponent', () => {
  let component: PropertyDetailsDrawerComponent;
  let fixture: ComponentFixture<PropertyDetailsDrawerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PropertyDetailsDrawerComponent, MapViewComponent],
    })
      .overrideComponent(PropertyDetailsDrawerComponent, {
        set: {
          imports: [MapViewComponent],
        },
      })
      .compileComponents();

    fixture = TestBed.createComponent(PropertyDetailsDrawerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should not display property details when selectedProperty is undefined', () => {
    component.selectedProperty = undefined;
    fixture.detectChanges();

    const detailsBox = fixture.debugElement.query(By.css('.details-box'));
    expect(detailsBox.children.length).toBe(
      0,
      'Details box should be empty when no property is selected',
    );
  });

  it('should display primary address when selectedProperty is provided', () => {
    component.selectedProperty = baseMockProperty;
    fixture.detectChanges();

    const primaryAddress = fixture.debugElement.query(By.css('.text-dark-gray'))
      .nativeElement.textContent;
    expect(primaryAddress).toContain(
      '123 Main St, Springfield, 62701',
      'Primary address should be displayed',
    );
  });

  it('should pass searchText and height to MapViewComponent', () => {
    component.selectedProperty = baseMockProperty;
    fixture.detectChanges();

    const mapView = fixture.debugElement.query(
      By.directive(MapViewComponent),
    ).componentInstance;
    expect(mapView.searchText).toBe(
      '123 Main St, Springfield, 62701',
      'MapViewComponent should receive correct searchText',
    );
    expect(mapView.height).toBe(
      400,
      'MapViewComponent should receive correct height',
    );
  });

  it('should display property details correctly', () => {
    component.selectedProperty = baseMockProperty;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    expect(compiled.querySelectorAll('p')[1].textContent).toContain(
      'Property PFI: 123',
    );
    expect(compiled.querySelectorAll('p')[2].textContent).toContain(
      'Status: ACTIVE',
    );
    expect(compiled.querySelectorAll('p')[3].textContent).toContain(
      'Title Type: FREEHOLD',
    );
    expect(compiled.querySelectorAll('p')[4].textContent).toContain(
      'Parcel: Parcel123',
    );
  });

  it('should display secondary address details when available', () => {
    const mockPropertyWithAlias: IPropertySearch = {
      ...baseMockProperty,
      aliasAddresses: [
        {
          eziAddress: 'Unit 1, 456 Oak St, Springfield, 62701',
          addressDetails: {
            buildingName: 'Oak Towers',
            unitType: 'Unit',
            unitNumber: '1',
            streetNumber: 456,
            streetName: 'Oak',
            streetType: 'St',
            streetSuffix: '',
            suburbTownLocality: 'Springfield',
            postcode: 62701,
          },
        },
      ],
    };
    component.selectedProperty = mockPropertyWithAlias;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    const secondaryAddress = compiled.querySelectorAll('p')[5].textContent;
    expect(secondaryAddress.trim()).toContain(
      'Unit 1, 456 Oak St, Springfield, 62701',
    );
    const detailedAddress = compiled.querySelectorAll('p')[6].textContent;
    expect(detailedAddress.trim()).toContain(
      'Oak Towers, Unit 1, 456 Oak St , Springfield 62701',
    );
  });

  it('should handle missing aliasAddresses gracefully', () => {
    component.selectedProperty = baseMockProperty;
    fixture.detectChanges();

    const compiled = fixture.nativeElement;
    const secondaryAddress =
      compiled.querySelectorAll('p')[5]?.textContent || '';
    expect(secondaryAddress).toBe(
      '',
      'Secondary address should not be displayed when aliasAddresses is empty',
    );
  });
});
