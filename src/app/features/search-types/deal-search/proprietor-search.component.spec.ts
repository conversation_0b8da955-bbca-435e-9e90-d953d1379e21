import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProprietorSearchComponent } from './proprietor-search.component';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SearchService } from '../../../core/services/search.service';
import { NotificationService } from '../../../core/services/notification.service';
import { CartService } from '../../../core/services/cart.service';
import { ProductSearchComponent } from '../product-search/product-search.component';
import { By } from '@angular/platform-browser';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { provideNoopAnimations } from '@angular/platform-browser/animations';

describe('ProprietorSearchComponent', () => {
  let component: ProprietorSearchComponent;
  let fixture: ComponentFixture<ProprietorSearchComponent>;
  let searchService: SearchService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ProprietorSearchComponent,
        NzSelectModule,
        NzInputModule,
        NzIconModule,
        FormsModule,
        CommonModule,
        ProductSearchComponent,
        HttpClientTestingModule,
      ],
      providers: [
        { provide: SearchService, useClass: SearchService },
        { provide: NotificationService, useClass: NotificationService },
        { provide: CartService, useClass: CartService },
        provideNoopAnimations(),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ProprietorSearchComponent);
    component = fixture.componentInstance;
    searchService = jasmine.createSpyObj('SearchService', [
      'getProprietors',
      'getProducts',
    ]);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render search input with placeholder and search icon', () => {
    const selectElement = fixture.debugElement.query(
      By.css('nz-select[data-testid="proprietorsearch-selectbox"]'),
    );
    expect(selectElement).toBeTruthy('Select box should be rendered');
    expect(selectElement.attributes['nzPlaceHolder']).toBe(
      'Enter Lot-Plan Number...',
      'Placeholder should be set',
    );
    const searchIcon = fixture.debugElement.query(
      By.css('i[nzType="search"][data-testid="volume-search-button"]'),
    );
    expect(searchIcon).toBeTruthy('Search icon should be rendered');
  });

  it('should display sample input text', () => {
    const sampleText = fixture.debugElement.query(By.css('.sample-input-text'))
      .nativeElement.textContent;
    expect(sampleText).toContain(
      'Sample input: warner',
      'Sample input text should be displayed',
    );
  });

  it('should show warning and not call searchService for search text shorter than 4 characters', () => {
    // spyOn(notificationService, 'warningMessage');
    // spyOn(searchService, 'getProprietors');
    component.onSearch('war');
    // expect(notificationService.warningMessage).toHaveBeenCalledWith(COMMON_STRINGS.warningMessages.addressSearchWarning);
    expect(searchService.getProprietors).not.toHaveBeenCalled();
    expect(component.isLoading).toBeFalse();
    expect(component.titleOptions).toEqual([]);
  });

  it('should render ProductSearchComponent with selected titleId', () => {
    component.selectedTitleId = { value: 'TITLE1', label: 'Title 1' };
    fixture.detectChanges();
    const productSearch = fixture.debugElement.query(
      By.directive(ProductSearchComponent),
    );
    expect(productSearch).toBeTruthy(
      'ProductSearchComponent should be rendered',
    );
    expect(productSearch.componentInstance.titleId).toBe(
      'TITLE1',
      'ProductSearchComponent should receive correct titleId',
    );
  });
});
