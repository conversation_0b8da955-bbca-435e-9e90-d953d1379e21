import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';

import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';

import {
  NzOptionComponent,
  NzSelectModule,
  NzSelectOptionInterface,
} from 'ng-zorro-antd/select';
import { SearchService } from '../../../core/services/search.service';
import { SEARCH_TABLE_COLUMNS } from '../../../core/tableColumns/search.column';
import { NotificationService } from '../../../core/services/notification.service';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { IProprietorSummary } from '../../../core/interface/proprietor-summary.interface';
import { ProductSearchComponent } from '../product-search/product-search.component';
import { BehaviorSubject, debounceTime } from 'rxjs';
import { CartService } from '../../../core/services/cart.service';

@Component({
  selector: 'app-proprietor-search',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzInputModule,
    NzIconModule,
    NzSelectModule,
    NzOptionComponent,
    ProductSearchComponent,
  ],
  templateUrl: './proprietor-search.component.html',
  styleUrls: ['./proprietor-search.component.scss', '../search-types.scss'],
})
export class ProprietorSearchComponent implements OnInit {
  proprietorSummaries: IProprietorSummary[] = [];
  titleOptions: NzSelectOptionInterface[] = [];
  isSearchPerformed = false;
  isLoading = false;
  searchChange$ = new BehaviorSubject('');

  tableColumns = SEARCH_TABLE_COLUMNS;
  selectedTitleId?: NzSelectOptionInterface;
  constructor(
    private searchService: SearchService,
    private notificationService: NotificationService,
    private cartService: CartService,
  ) {}

  ngOnInit(): void {
    this.searchChange$.pipe(debounceTime(500)).subscribe({
      next: (lastName) => this.getProprietors(lastName),
    });
  }

  private getProprietors(lastName: string): void {
    if (!lastName?.trim()) {
      return;
    }

    this.isLoading = true;
    this.isSearchPerformed = true;

    this.searchService.getProprietors(lastName).subscribe({
      next: (results) => this.handleSearchResults(results),
      error: (error: HttpErrorResponse) => this.handleSearchError(error),
    });
  }

  private handleSearchResults(results: IProprietorSummary[]): void {
    this.proprietorSummaries = results;
    this.titleOptions = results
      .map((result) => result.titleIdOptions)
      .filter((x) => !!x);
    this.isLoading = false;
  }

  private handleSearchError(error: HttpErrorResponse): void {
    console.error('Search error:', error);
    this.isSearchPerformed = false;
    this.isLoading = false;
  }

  private search(searchText: string): void {
    if (!searchText) {
      this.notificationService.warningMessage(
        COMMON_STRINGS.warningMessages.addressSearchWarning,
      );
      return;
    }
    this.searchChange$.next(searchText);
  }

  onSearch(searchText: string | null) {
    if (searchText && searchText.trim().length > 3) {
      this.search(searchText.trim());
    } else {
      this.isLoading = false;
      this.titleOptions = [];
    }
  }
}
