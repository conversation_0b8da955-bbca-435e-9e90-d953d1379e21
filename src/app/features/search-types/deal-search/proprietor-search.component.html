<div class="page-container">
  <!-- Top Section -->
  <div class="top-section">
    <div class="search-row">
      <!-- Input Group + Icon in same row -->
      <div class="search-container">
        <!-- Labels -->
        <div class="proprietor-search">Proprietor Search</div>
       

        <!-- Input row: input and icon inline -->
        <nz-select 
          nzPlaceHolder="Enter Lot-Plan Number..." 
          [nzShowSearch]="true" 
          [nzServerSearch]="true"
          [nzAllowClear]="true"
          [nzShowArrow]="true" 
          [nzSuffixIcon]="suffixIconSearch"
          [nzLoading]="isLoading" 
          [(ngModel)]="selectedTitleId" 
          (nzOnSearch)="onSearch($event)" 
          data-testid="proprietorsearch-selectbox"
          >
          @if (!isLoading) {
            @for (option of titleOptions; track option) {
              <nz-option [nzValue]="option" [nzLabel]="option.label + ''"></nz-option>
            }
          } @else {
            <nz-option nzDisabled nzCustomContent>
              <nz-icon nzType="loading" class="loading-icon" />
              Loading Data...
            </nz-option>
          }
        </nz-select>

        <ng-template #suffixIconSearch>
          <i
            nz-icon
            nzType="search"
            class="search-icon-clickable"
            data-testid="volume-search-button"
          ></i>
        </ng-template>
        <!-- Sample text -->
        <div class="sample-input-text">
          Sample input:  warner
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div *ngIf="selectedTitleId" class="data-grid-container">
      <app-product-search
        [titleId]="selectedTitleId.value"
      ></app-product-search>
  </div>
</div>
