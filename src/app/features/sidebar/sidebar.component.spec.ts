// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { SidebarComponent } from './sidebar.component';
// import { RouterTestingModule } from '@angular/router/testing';
// import { By } from '@angular/platform-browser';

// describe('SidebarComponent', () => {
//   let component: SidebarComponent;
//   let fixture: ComponentFixture<SidebarComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [SidebarComponent],
//       imports: [RouterTestingModule], // Import RouterTestingModule for routerLink testing
//     }).compileComponents();
//   });

//   beforeEach(() => {
//     fixture = TestBed.createComponent(SidebarComponent);
//     component = fixture.componentInstance;

//     // Mock data for menuItems and footerItems
//     component.menuItems = [
//       { label: 'Home', route: '/home', icon: 'home', hasArrow: false },
//       { label: 'Settings', route: '/settings', icon: 'cog', hasArrow: true },
//     ];
//     component.footerItems = [
//       // eslint-disable-next-line @typescript-eslint/no-empty-function
//       { label: 'Logout', icon: 'sign-out', action: () => {} },
//     ];
//     component.isLoggedIn = true;
//     component.username = 'John Doe';
//     component.email = '<EMAIL>';

//     fixture.detectChanges();
//   });

//   it('should create the sidebar component', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should display the logo text', () => {
//     const logoElement = fixture.debugElement.query(By.css('.logo-text')).nativeElement;
//     expect(logoElement.textContent).toContain('Areadocs');
//   });

//   it('should render the correct number of menu items', () => {
//     const menuItems = fixture.debugElement.queryAll(By.css('.menu-item'));
//     expect(menuItems.length).toBe(component.menuItems.length);
//   });

//   it('should display the correct menu item labels', () => {
//     const menuItems = fixture.debugElement.queryAll(By.css('.menu-item span'));
//     menuItems.forEach((item, index) => {
//       expect(item.nativeElement.textContent).toContain(component.menuItems[index].label);
//     });
//   });

//   it('should display the user profile when logged in', () => {
//     const userProfile = fixture.debugElement.query(By.css('.user-profile'));
//     expect(userProfile).toBeTruthy();

//     const usernameElement = userProfile.query(By.css('.username')).nativeElement;
//     const emailElement = userProfile.query(By.css('.email')).nativeElement;

//     expect(usernameElement.textContent).toContain(component.username);
//     expect(emailElement.textContent).toContain(component.email);
//   });

//   it('should render the correct number of footer items', () => {
//     const footerItems = fixture.debugElement.queryAll(By.css('.footer-item'));
//     expect(footerItems.length).toBe(component.footerItems.length);
//   });

//   it('should call the action method when a footer item is clicked', () => {
//     spyOn(component.footerItems[0], 'action');
//     const footerItem = fixture.debugElement.query(By.css('.footer-item'));
//     footerItem.triggerEventHandler('click', null);
//     expect(component.footerItems[0].action).toHaveBeenCalled();
//   });
// });
