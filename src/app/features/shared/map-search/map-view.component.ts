import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MapService } from '../../../core/services/map.service';
import { MapOptions } from '../../../core/models/mapOptions';
import * as MapEnum from '../../../core/enumerations/mapEnum';
import { LatLng } from '../../../core/models/latLng';
import { Observable, Subscription } from 'rxjs';
import { NotificationService } from '../../../core/services/notification.service';

@Component({
  selector: 'app-map-view',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './map-view.component.html',
  styleUrls: ['./map-view.component.scss'],
})
export class MapViewComponent implements OnInit, OnDestroy {
  @Input() searchText = '';
  @Input() search$?: Observable<void>;
  @Input() showSearchBox = true;
  @Input() height = 600;
  public map!: google.maps.Map;
  public isLoading = false;
  public errorMessage = '';

  private searchSubscription?: Subscription;
  private initialLatitude = -37.814;
  private initialLongitude = 144.96332;
  private neutralPin?: google.maps.marker.AdvancedMarkerElement;
  private mapService = inject(MapService);
  private notificationService = inject(NotificationService);
  @Output() searchTextChange = new EventEmitter<string>();

  async ngOnInit(): Promise<void> {
    await this.initMap();
    this.searchSubscription = this.search$?.subscribe({
      next: () => {
        this.searchAddress();
      },
    });
  }

  ngOnDestroy(): void {
    this.searchSubscription?.unsubscribe();
  }

  private async initMap() {
    // Initialize map options
    const mapOptions = new MapOptions('map-canvas');
    mapOptions.SetBasicOptions(
      MapEnum.MapType.Roadmap,
      9, // Zoom level
      7,
      null,
      this.initialLatitude,
      this.initialLongitude,
    );
    mapOptions.RequireCtrlToZoom = false;
    mapOptions.FeaturesToHide = [
      MapEnum.MapFeatures.LineTransit,
      MapEnum.MapFeatures.AirportStation,
      MapEnum.MapFeatures.BusStation,
      MapEnum.MapFeatures.RailwayStation,
      MapEnum.MapFeatures.AttractionPin,
      MapEnum.MapFeatures.BusinessPin,
      MapEnum.MapFeatures.GovernmentPin,
      MapEnum.MapFeatures.MedicalInstitutionPin,
      MapEnum.MapFeatures.ParkPin,
      MapEnum.MapFeatures.PlaceOfWorkshipPin,
      MapEnum.MapFeatures.ScoolPin,
      MapEnum.MapFeatures.SportsComplexPin,
    ];

    // Create map
    this.map = await this.mapService.CreateMap(mapOptions, !this.searchText);

    // Add search box
    if (this.showSearchBox) {
      await this.mapService.AddSearchBox(
        this.map,
        'searchbox',
        MapEnum.GoogleMapControlPosition.Top_Left,
        true,
        (place) => this.handleSearchResult(place),
      );
    }

    // Programmatically search for an address
    if (this.searchText) {
      await this.searchAddress();
    }
  }

  private async handleSearchResult(place: google.maps.places.PlaceResult) {
    this.isLoading = true;
    this.errorMessage = '';
    this.mapService.ClearSingleMarker(this.neutralPin);

    if (!place.geometry?.location) {
      this.errorMessage = 'No valid location found for the address.';
      this.notificationService.errorMessage(this.errorMessage);
      this.isLoading = false;
      return;
    }

    // Place marker at searched location
    const latlng = new LatLng();
    latlng.Latitude = place.geometry.location.lat();
    latlng.Longitude = place.geometry.location.lng();
    if (this.neutralPin) this.mapService.ClearSingleMarker(this.neutralPin);
    this.neutralPin = await this.mapService.PlaceMarker(
      this.map,
      latlng.Latitude,
      latlng.Longitude,
      false,
    );

    // Center map on the marker
    this.mapService.SetCenter(this.map, latlng.Latitude, latlng.Longitude);
    this.mapService.SetMapZoomLevel(this.map, 18);

    // Display property info in an info window
    // this.setupInfoWindow(this.neutralPin, place, latlng);
    this.isLoading = false;
  }

  async searchAddress() {
    if (this.searchText) {
      const address = this.searchText;
      this.mapService.ClearSingleMarker(this.neutralPin);
      const place = await this.mapService.SearchAddress(address);

      if (place) {
        await this.handleSearchResult(place);
      }
    }
  }
}
