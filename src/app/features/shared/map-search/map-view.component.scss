.map-container {
  position: relative;
  height: 100%;
  width: 100%;
}

#searchbox {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
}

.error {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff4d4d;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
}

#searchbox {
    font-size: 12px;
    width: 250px;
    margin-top: 10px;
}