<div class="map-container">
  <div id="map-canvas" [style.height.px]="height" style="width: 100%;"></div>
  <div >
    <input id="searchbox" [(ngModel)]="searchText" (ngModelChange)="searchTextChange.emit(searchText)" type="text" (keyup.enter)="searchAddress()" class="form-control" *ngIf="showSearchBox">
  </div>
  <div *ngIf="isLoading" class="loading">Loading...</div>
  <div *ngIf="errorMessage" class="error">{{ errorMessage }}</div>
</div>