import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { By } from '@angular/platform-browser';
import { ModalComponent } from './modal.component';
import { provideAnimations } from '@angular/platform-browser/animations';
import { NzModalModule } from 'ng-zorro-antd/modal';

@Component({
  template: `
    <ng-template #bodyTpl>Modal Body</ng-template>
    <ng-template #footerTpl>Modal Footer</ng-template>

    <app-imperium-modal
      [visible]="visible"
      [modalTitle]="title"
      [bodyTemplate]="bodyTpl"
      [footerTemplate]="footerTpl"
      [centered]="true"
      [width]="'large'"
      (visibleChange)="onVisibleChange($event)"
    ></app-imperium-modal>
  `,
  imports: [ModalComponent],
})
class TestHostComponent {
  visible = true;
  title: string | TemplateRef<object> = 'Test Title';
  @ViewChild('bodyTpl') bodyTpl!: TemplateRef<object>;
  @ViewChild('footerTpl') footerTpl!: TemplateRef<object>;

  emittedValue: boolean | null = null;

  onVisibleChange(value: boolean) {
    this.emittedValue = value;
  }
}

describe('ModalComponent', () => {
  let fixture: ComponentFixture<TestHostComponent>;
  let hostComponent: TestHostComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModalComponent, TestHostComponent, NzModalModule],
      providers: [provideAnimations()],
    }).compileComponents();

    fixture = TestBed.createComponent(TestHostComponent);
    hostComponent = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the test host', () => {
    expect(hostComponent).toBeTruthy();
  });

  it('should bind title as string', () => {
    fixture.detectChanges();
    const titleEl = document.body.querySelector('.ant-modal-title');
    expect(titleEl?.textContent).toContain('Test Title');
  });

  it('should display body content via ngTemplateOutlet', () => {
    fixture.detectChanges();
    const modalBody = document.body.querySelector('.ant-modal-body');
    expect(modalBody?.textContent).toContain('Modal Body');
  });

  it('should display footer content via ngTemplateOutlet', () => {
    fixture.detectChanges();
    const modalFooter = document.body.querySelector('.ant-modal-footer');
    expect(modalFooter).toBeTruthy();
  });

  it('should emit visibleChange when onClose() is called', () => {
    const modalComponent = fixture.debugElement.query(
      By.directive(ModalComponent),
    ).componentInstance;
    modalComponent.onClose();
    fixture.detectChanges();
    expect(hostComponent.emittedValue).toBeFalse();
  });

  it('should return correct width value from getter', () => {
    const modalComponent = fixture.debugElement.query(
      By.directive(ModalComponent),
    ).componentInstance;
    expect(modalComponent.nzWidth).toBe('65vw');
  });

  it('should return isTitleString as true when title is string', () => {
    const modalComponent = fixture.debugElement.query(
      By.directive(ModalComponent),
    ).componentInstance;
    expect(modalComponent.isTitleString).toBeTrue();
  });

  it('should return stringModalTitle when title is a string', () => {
    const modalComponent = fixture.debugElement.query(
      By.directive(ModalComponent),
    ).componentInstance;
    expect(modalComponent.stringModalTitle).toBe('Test Title');
  });

  it('should return correct isVisible state from getter', () => {
    const modalComponent = fixture.debugElement.query(
      By.directive(ModalComponent),
    ).componentInstance;
    expect(modalComponent.isVisible).toBeTrue();
  });
});
