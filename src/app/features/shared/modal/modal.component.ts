import { CommonModule } from '@angular/common';
import { Component, input, output, TemplateRef } from '@angular/core';

import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPageHeaderModule } from 'ng-zorro-antd/page-header';
import { NzIconModule } from 'ng-zorro-antd/icon';

import { TModalDimentionTypes } from '../../../core/types/modalSize.type';

@Component({
  selector: 'app-imperium-modal',
  imports: [NzModalModule, CommonModule, NzPageHeaderModule, NzIconModule],
  templateUrl: './modal.component.html',
  styleUrl: './modal.component.scss',
})
export class ModalComponent {
  visible = input.required<boolean>();
  modalTitle = input.required<string | TemplateRef<object>>();
  bodyTemplate = input.required<TemplateRef<object>>();

  footerTemplate = input<TemplateRef<object> | null>(null);
  centered = input(true);
  width = input<TModalDimentionTypes>('large');

  visibleChange = output<boolean>();

  onClose = () => {
    this.isVisible = false;
  };

  // getters & setters
  get isVisible(): boolean {
    return this.visible();
  }

  set isVisible(visible: boolean) {
    this.visibleChange.emit(visible);
  }

  get isTitleString(): boolean {
    return typeof this.modalTitle() === 'string';
  }

  get stringModalTitle(): string {
    return this.isTitleString ? (this.modalTitle() as string) : '';
  }

  get nzWidth(): string | number {
    switch (this.width()) {
      case 'xs-small':
        return '30vw';
      case 'small':
        return '40vw';
      case 'default':
        return 416;
      case 'medium':
        return '55vw';
      case 'large':
        return '65vw';
      case 'xl-large':
        return '70vw';
      case 'xxl-large':
        return '80vw';
      case 'xxxl-large':
        return '90vw';
    }
  }
}
