<nz-modal [(nzVisible)]="isVisible" [nzWidth]="nzWidth" [nzTitle]="isTitleString ? titleTemplate : modalTitle()" [nzFooter]="footerTemplate()"
  [nzCentered]="centered()" (nzOnCancel)="onClose()" nzDraggable>

  <div style="max-height: 70vh; overflow-y: auto;" *nzModalContent>
    <ng-template [ngTemplateOutlet]="bodyTemplate()"></ng-template>
  </div>

  <ng-template *nzModalFooter [ngTemplateOutlet]="footerTemplate()"></ng-template>
</nz-modal>

<ng-template #titleTemplate>
  <div class="modal-title" *ngIf="isTitleString">{{modalTitle()}}</div>
</ng-template>

<!-- 

  *********************************Example Usage*****************************************

    <app-imperium-modal [(visible)]="isVisible" [modalTitle]="'Testing'" [bodyTemplate]="bodyTemplate"
      [footerTemplate]="footerTemplate">

      // Body Template
      <ng-template #bodyTemplate>
        
        Body Template

      </ng-template>

      // Optional
      <ng-template #footerTemplate>

        <button nz-button nzType="primary" (click)="isVisible = false">Close</button>

      </ng-template>

    </app-imperium-modal>

-->
