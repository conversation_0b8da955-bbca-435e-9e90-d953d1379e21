import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faBell, faShoppingCart } from '@fortawesome/free-solid-svg-icons';

import { AddressSearchComponent } from '../search-types/address-search/address-search.component';
import { MapSearchComponent } from '../search-types/map-search/map-search.component';
import { VolumeSearchComponent } from '../search-types/volume-search/volume-search.component';
import { PlanSearchComponent } from '../search-types/plan-search/plan-search.component';
import { ProprietorSearchComponent } from '../search-types/deal-search/proprietor-search.component';
import { PropertySearchComponent } from '../search-types/property-search/property-search.component';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import {
  EnumSearchType,
  EnumSearchTypeHeader,
  EnumSearchTabIndex,
} from '../../core/enumerations/search-type';

@Component({
  selector: 'app-homepage',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AddressSearchComponent,
    MapSearchComponent,
    VolumeSearchComponent,
    ProprietorSearchComponent,
    PlanSearchComponent,
    PropertySearchComponent,
    NzTabsModule,
    NzDropDownModule,
    FontAwesomeModule,
    NzIconModule,
    NzMenuModule,
  ],
  templateUrl: './homepage.component.html',
  styleUrls: ['./homepage.component.scss'],
})
export class HomepageComponent {
  faShoppingCart = faShoppingCart;
  faBell = faBell;

  isSidebarCollapsed = window.innerWidth < 1000;
  isSearchPerformed = false;

  selectedTabIndex: EnumSearchTabIndex = EnumSearchTabIndex.Property;
  selectedChildIndex: Record<number, number> = {};
  currentSearch = EnumSearchType.Address;

  SearchType = EnumSearchType;
  EnumSearchTabIndex = EnumSearchTabIndex;

  searchTypes = [
    {
      tab: EnumSearchType.Property,
      children: [
        EnumSearchType.Address,
        EnumSearchType.Map,
        EnumSearchType.Plan,
      ],
    },
    {
      tab: EnumSearchType.Document,
      children: [EnumSearchType.Title, EnumSearchType.Proprietor],
    },
    {
      tab: EnumSearchType.Alerts,
      children: [],
    },
  ];

  onTabChange(parentIndex: EnumSearchTabIndex): void {
    this.selectedTabIndex = parentIndex;
    const parent = this.searchTypes[parentIndex];

    if (parent.children?.length) {
      const selectedChild = this.selectedChildIndex[parentIndex] ?? 0;
      this.currentSearch = parent.children[selectedChild];
    } else {
      this.currentSearch = parent.tab;
    }
  }

  onChildTabChange(childIndex: number, parentIndex: EnumSearchTabIndex): void {
    this.selectedChildIndex[parentIndex] = childIndex;
    this.currentSearch = this.searchTypes[parentIndex].children[childIndex];
  }

  onSearchPerformed(): void {
    this.isSearchPerformed = true;
  }

  formatLabel(type: EnumSearchType): string {
    switch (type) {
      case EnumSearchType.Address:
        return EnumSearchTypeHeader.Address;
      case EnumSearchType.Map:
        return EnumSearchTypeHeader.Map;
      case EnumSearchType.Plan:
        return EnumSearchTypeHeader.Plan;
      case EnumSearchType.Property:
        return EnumSearchTypeHeader.Property;
      case EnumSearchType.Document:
        return EnumSearchTypeHeader.Document;
      case EnumSearchType.Title:
        return EnumSearchTypeHeader.Title;
      case EnumSearchType.Proprietor:
        return EnumSearchTypeHeader.Proprietor;
      case EnumSearchType.Alerts:
        return EnumSearchTypeHeader.Alerts;
      default:
        return type;
    }
  }
}
