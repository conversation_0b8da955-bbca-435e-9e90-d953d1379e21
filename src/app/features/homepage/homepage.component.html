<div class="featured-search-container">
  <!-- Wrapper to control horizontal alignment -->
  <div class="tab-wrapper">
    <!-- Top-level Tabs -->
    <div class="parent-tab-bar">
      <div
        class="tab"
        [class.active]="selectedTabIndex === EnumSearchTabIndex.Property"
        (click)="onTabChange(EnumSearchTabIndex.Property)"
      >
        {{ formatLabel(searchTypes[EnumSearchTabIndex.Property].tab) }}
      </div>
      <div
        class="tab"
        [class.active]="selectedTabIndex === EnumSearchTabIndex.Document"
        (click)="onTabChange(EnumSearchTabIndex.Document)"
      >
        {{ formatLabel(searchTypes[EnumSearchTabIndex.Document].tab) }}
      </div>
      <div
        class="tab"
        [class.active]="selectedTabIndex === EnumSearchTabIndex.Alerts"
        (click)="onTabChange(EnumSearchTabIndex.Alerts)"
      >
        {{ formatLabel(searchTypes[EnumSearchTabIndex.Alerts].tab) }}
      </div>
    </div>

    <!-- Child Tabs -->
    <ul
      class="child-tabs"
      *ngIf="searchTypes[selectedTabIndex]?.children?.length"
    >
      <li
        *ngFor="
          let child of searchTypes[selectedTabIndex].children;
          let j = index
        "
        [class.active]="(selectedChildIndex[selectedTabIndex] ?? 0) === j"
        (click)="onChildTabChange(j, selectedTabIndex)"
      >
        {{ formatLabel(child) }}
      </li>
    </ul>
  </div>

  <!-- Search Content -->
  <div class="search-content">
    <ng-container [ngSwitch]="currentSearch">
      <app-property-search
        *ngSwitchCase="SearchType.Property"
      ></app-property-search>

      <app-address-search
        *ngSwitchCase="SearchType.Address"
        (searchPerformed)="onSearchPerformed()"
      ></app-address-search>

      <app-map-search
        *ngSwitchCase="SearchType.Map"
        (searchPerformed)="onSearchPerformed()"
      ></app-map-search>

      <app-volume-search
        *ngSwitchCase="SearchType.Title"
        (searchPerformed)="onSearchPerformed()"
      ></app-volume-search>

      <app-proprietor-search
        *ngSwitchCase="SearchType.Proprietor"
        (searchPerformed)="onSearchPerformed()"
      ></app-proprietor-search>

      <app-plan-search
        *ngSwitchCase="SearchType.Plan"
        (searchPerformed)="onSearchPerformed()"
      ></app-plan-search>

      <div *ngSwitchCase="SearchType.Alerts">
        <!-- Alerts coming soon -->
      </div>
    </ng-container>
  </div>
</div>
