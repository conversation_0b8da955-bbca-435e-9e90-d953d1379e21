.featured-search-container {
  .tab-wrapper {
    padding: 0 24px;
  }

  .parent-tab-bar {
    display: flex;
    gap: 12px;
    background: #f5f5f5;
    border-radius: 8px;
    margin-top: 15px;
    align-items: center;
    justify-content: space-between;
  }

  .tab {
    padding: 16px 24px;
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    color: #666;
    background: #f5f5f5;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    &.active {
      color: #4a90e2;
      font-weight: 600;
    }
  }

  .child-tabs {
    display: inline-flex;
    list-style: none;
    margin: 12px 0 0 0;
    padding: 0 24px;
    background: #f5f5f5;
    border-top: 1px solid #e8e8e8;
    border-radius: 4px;
    align-items: flex-start;
  }

  .child-tabs li {
    font-size: 14px;
    cursor: pointer;
    padding: 12px 20px;
    color: #666;
    background: #f5f5f5;
    transition: all 0.3s;

    &.active {
      color: #4a90e2;
      font-weight: 600;
    }
  }

  .search-content {
    background-color: #fff;
    padding: 24px;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin: 0 24px;
  }
}
