// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { DataGridComponent } from './data-grid.component';
// import { CommonModule } from '@angular/common';

// describe('DataGridComponent', () => {
//   let component: DataGridComponent;
//   let fixture: ComponentFixture<DataGridComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [CommonModule], // Import CommonModule as the component is standalone
//       declarations: [DataGridComponent],
//     }).compileComponents();
//   });

//   beforeEach(() => {
//     fixture = TestBed.createComponent(DataGridComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create the component', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should display rows when data is provided', () => {
//     component.data = [
//       { id: 1, name: 'Test 1' },
//       { id: 2, name: 'Test 2' },
//     ] as any; // Mock data
//     fixture.detectChanges();

//     expect(component.displayRows.length).toBe(2);
//     expect(component.displayRows[0].name).toBe('Test 1');
//   });

//   it('should generate empty rows when no data is provided', () => {
//     component.data = [];
//     fixture.detectChanges();

//     expect(component.displayRows.length).toBe(7); // Default empty rows
//   });
// });
