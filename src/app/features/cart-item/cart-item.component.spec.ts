import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of } from 'rxjs';
import { CartItemDTO, UserResponseDTO } from '../../api-client';
import { CartItemComponent } from './cart-item.component';
import { CartService } from '../../core/services/cart.service';
import { NotificationService } from '../../core/services/notification.service';
import { RechargeService } from '../../core/services/recharge.service';
import { UserService } from '../../core/services/user.service';
import { SharedService } from '../../core/services/shared.service';
import { TransactionService } from '../../core/services/transaction.service';
import { COMMON_STRINGS } from '../../core/constants/common';

describe('CartItemComponent', () => {
  let component: CartItemComponent;
  let fixture: ComponentFixture<CartItemComponent>;
  let cartServiceSpy: jasmine.SpyObj<CartService>;
  let notificationServiceSpy: jasmine.SpyObj<NotificationService>;
  let rechargeServiceSpy: jasmine.SpyObj<RechargeService>;
  let userServiceSpy: jasmine.SpyObj<UserService>;
  let sharedServiceSpy: jasmine.SpyObj<SharedService>;
  let transactionServiceSpy: jasmine.SpyObj<TransactionService>;
  let routerSpy: jasmine.SpyObj<Router>;
  let activatedRouteMock: Partial<ActivatedRoute>;

  const mockCartItems: CartItemDTO[] = [
    { id: 1, productCode: 'PROD1', price: 50, tax: 5, finalPrice: 55 },
  ];

  const mockUser: UserResponseDTO = {
    id: 1,
    firstName: 'John',
    email: '<EMAIL>',
    roleName: 'User',
    contactNumber: '1234567890',
    roleId: 1,
  };

  beforeEach(async () => {
    cartServiceSpy = jasmine.createSpyObj('CartService', [
      'getCart',
      'removeFromCart',
    ]);
    notificationServiceSpy = jasmine.createSpyObj('NotificationService', [
      'successMessage',
      'error',
      'showLoaderMessage',
      'hideLoaderMessage',
    ]);
    rechargeServiceSpy = jasmine.createSpyObj('RechargeService', [
      'rechargeWallet',
      'getUserWalletBalance',
    ]);
    userServiceSpy = jasmine.createSpyObj('UserService', ['getUserRole'], {
      userInfo: mockUser,
    });
    sharedServiceSpy = jasmine.createSpyObj('SharedService', [], {
      cartItems: [],
    });
    transactionServiceSpy = jasmine.createSpyObj('TransactionService', [
      'getAllTransactions',
    ]);
    routerSpy = jasmine.createSpyObj('Router', ['navigate'], { events: of() });

    activatedRouteMock = {
      queryParams: of({ paymentAmount: 100, transactionId: 1 }),
    };

    cartServiceSpy.getCart.and.returnValue(of({ items: mockCartItems }));
    userServiceSpy.getUserRole.and.returnValue(Promise.resolve(mockUser));
    rechargeServiceSpy.getUserWalletBalance.and.returnValue(of(100));
    transactionServiceSpy.getAllTransactions.and.returnValue(of([]));

    await TestBed.configureTestingModule({
      imports: [CartItemComponent, HttpClientTestingModule],
      providers: [
        { provide: CartService, useValue: cartServiceSpy },
        { provide: NotificationService, useValue: notificationServiceSpy },
        { provide: RechargeService, useValue: rechargeServiceSpy },
        { provide: UserService, useValue: userServiceSpy },
        { provide: SharedService, useValue: sharedServiceSpy },
        { provide: TransactionService, useValue: transactionServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CartItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should remove an item and reload cart', fakeAsync(() => {
    cartServiceSpy.removeFromCart.and.returnValue(of({}));
    component.removeItem(1);
    tick();
    expect(cartServiceSpy.removeFromCart).toHaveBeenCalledWith(1);
    expect(cartServiceSpy.getCart).toHaveBeenCalled();
    expect(notificationServiceSpy.successMessage).toHaveBeenCalledWith(
      COMMON_STRINGS.successMessages.removeFromCartSuccess,
    );
  }));
});
