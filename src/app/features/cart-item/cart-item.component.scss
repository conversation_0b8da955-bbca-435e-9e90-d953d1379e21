.fixed-splitter {
  display: flex;
  flex-direction: column;
}

.left-panel, .right-panel {
  padding: 24px;
  overflow-y: auto;
}

.left-panel {
  background-color: #fafafa;
}

.right-panel {
  background-color: #f0f9ff;
  min-height: 100vh;
}

.wallet-card, .summary-card {
  border-radius: 12px;
  padding: 20px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.wallet-balance {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
  margin: 10px 0 16px;
}

.order-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  }
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.left {
  display: flex;
  flex-direction: column;
  gap: 4px;

  small {
    color: #666;
  }
}

.doc-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #2c3e50;

  i {
    margin-right: 8px;
    color: #409eff;
  }
}

.right {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
}

.price {
  font-weight: 600;
  font-size: 16px;
  color: #2d3436;
}

.delete-icon {
  color: #ff4d4f;
  cursor: pointer;
  font-size: 18px;

  &:hover {
    color: #d9363e;
  }
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 15px;

  &.total {
    font-weight: bold;
    font-size: 16px;
    margin-top: 12px;
  }
}

.pay-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  background-color: #141414;
  color: #ffffff;
  border: none;
  border-radius: 8px;

  &:hover {
    background-color: #000000;
  }
}

.drawer-breadcrumb {
  background: #f8f9fa;
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;

  ::ng-deep .ant-breadcrumb {
    font-size: 14px;

    .ant-breadcrumb-link {
      color: #666;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #1890ff;
      }
    }

    .ant-breadcrumb-separator {
      color: #999;
    }
  }
}

@media (max-width: 768px) {
  .right-panel {
    padding: 15px;
  }

  .order-card .card-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    .right {
      width: 100%;
      justify-content: space-between;
    }
  }
}
