<div style="height: 100vh" class="fixed-splitter">
  <div>
    <div class="right-panel">
      <!-- Cart View -->
      <div *ngIf="currentState === PaymentState.CART && !showRechargeView">
        <h3>Order Now</h3>
        <nz-card [nzBordered]="false" class="wallet-card">
          <p>
            <strong><u>Current wallet balance</u></strong>
          </p>
          <h2 class="wallet-balance">${{ walletBalance.toFixed(2) }}</h2>
          <button
            nz-button
            nzType="primary"
            data-testid="recharge-wallet"
            (click)="rechargeWallet()"
          >
            Recharge wallet
          </button>
        </nz-card>

        <h3>Review your Orders</h3>
        <nz-list
          [nzDataSource]="orders"
          [nzRenderItem]="item"
          [nzBordered]="false"
          nzItemLayout="vertical"
        >
          <ng-template #item let-item>
            <nz-card class="order-card" [nzBordered]="false">
              <div class="card-content">
                <div class="left">
                  <div class="doc-title">
                    <i nz-icon nzType="file-text" nzTheme="outline"></i>
                    <span>{{ item.productName }}</span>
                  </div>
                  <small>{{ item.productDescription }}</small>
                </div>
                <div class="right">
                  <span class="price">${{ item.price }}</span>
                  <i
                    nz-icon
                    nzType="delete"
                    nzTheme="outline"
                    class="delete-icon"
                    data-testid="delete-cart-item"
                    (click)="removeItem(item.id)"
                  ></i>
                </div>
              </div>
            </nz-card>
          </ng-template>
        </nz-list>

        <nz-card [nzBordered]="false" class="summary-card">
          <h4>Payment summary</h4>
          <ng-container *ngFor="let order of orders; let i = index">
            <div class="summary-row">
              {{ order.productCode }} price:
              <span>${{ order.price?.toFixed(2) }}</span>
            </div>
            <div class="summary-row">
              Tax (GST): <span>${{ order.tax?.toFixed(2) }}</span>
            </div>
          </ng-container>

          <nz-divider></nz-divider>

          <div class="summary-row total">
            <strong>Total:</strong>
            <strong>${{ finalPrice.toFixed(2) || '0.00' }}</strong>
          </div>
        </nz-card>

        <button
          nz-button
          nzType="default"
          class="pay-btn"
          data-testid="pay-btn"
          (click)="createOrder()"
        >
          Pay ${{ finalPrice.toFixed(2) || '0.00' }}
        </button>
      </div>

      <!-- Recharge View -->
      <div *ngIf="showRechargeView">
        <!-- Breadcrumb stays here -->
        <div class="drawer-breadcrumb">
          <nz-breadcrumb>
            <nz-breadcrumb-item>
              <a (click)="showRechargeView = false">
                <span>Cart</span>
              </a>
            </nz-breadcrumb-item>
            <nz-breadcrumb-item>
              <span>Recharge</span>
            </nz-breadcrumb-item>
          </nz-breadcrumb>
        </div>

        <!-- Wallet Component handles Recharge UI -->
        <app-wallet
          [(amount)]="amount"
          [visibleWallet]="visible"
          (visibleWalletChange)="visibleChange.emit($event)"
          (visibleWalletAfterDelay)="visibleAfterDelay.emit($event)"
        ></app-wallet>
      </div>
    </div>
  </div>
</div>
