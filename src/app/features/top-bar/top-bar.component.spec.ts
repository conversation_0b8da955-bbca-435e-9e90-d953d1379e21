import { CommonModule } from '@angular/common';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { of, Subject, throwError } from 'rxjs';

import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzButtonModule } from 'ng-zorro-antd/button';

import { CartService } from '../../core/services/cart.service';
import { SharedService } from '../../core/services/shared.service';
import { UserService } from '../../core/services/user.service';
import { EnumUserRole } from '../../core/enumerations/user-roles';
import { TopBarComponent } from './top-bar.component';
import { CartItemComponent } from '../cart-item/cart-item.component';

// Import the provider

describe('TopBarComponent', () => {
  let component: TopBarComponent;
  let fixture: ComponentFixture<TopBarComponent>;
  let cartService: jasmine.SpyObj<CartService>;
  let sharedService: jasmine.SpyObj<SharedService>;
  let userService: jasmine.SpyObj<UserService>;
  let activatedRouteMock: Partial<ActivatedRoute>;

  // Mock services
  beforeEach(async () => {
    const cartServiceSpy = jasmine.createSpyObj<CartService>('CartService', [
      'getCart',
      'onCartUpdated',
    ]);
    const sharedServiceSpy = jasmine.createSpyObj<SharedService>(
      'SharedService',
      ['cartItems'],
    );
    // Define userInfo as a writable property in the mock
    const userServiceSpy = jasmine.createSpyObj<UserService>(
      'UserService',
      ['getUserRole'],
      {
        userInfo: { roleId: EnumUserRole.INDEPENDEDNT_AGENT }, // Initial value
      },
    );

    // Make userInfo writable by overriding the property descriptor
    let userInfo = { roleId: EnumUserRole.INDEPENDEDNT_AGENT };
    Object.defineProperty(userServiceSpy, 'userInfo', {
      get: () => userInfo,
      set: (value) => (userInfo = value),
      configurable: true,
    });
    activatedRouteMock = {
      queryParams: of({}),
    };

    await TestBed.configureTestingModule({
      imports: [
        TopBarComponent, // Standalone component
        HttpClientTestingModule,
        CommonModule,
        FontAwesomeModule,
        NzBadgeModule,
        NzIconModule,
        NzDrawerModule,
        NzButtonModule,
        CartItemComponent,
      ],
      providers: [
        { provide: CartService, useValue: cartServiceSpy },
        { provide: SharedService, useValue: sharedServiceSpy },
        { provide: UserService, useValue: userServiceSpy },
        { provide: ActivatedRoute, useValue: activatedRouteMock },
        provideNoopAnimations(), // Add this to disable animations in tests
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TopBarComponent);
    component = fixture.componentInstance;
    cartService = TestBed.inject(CartService) as jasmine.SpyObj<CartService>;
    sharedService = TestBed.inject(
      SharedService,
    ) as jasmine.SpyObj<SharedService>;
    userService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;

    // Mock cart service responses
    cartService.getCart.and.returnValue(
      of({ itemCount: 5, items: [{ id: 1, name: 'Item 1' }] }),
    );
    const cartUpdateSubject = new Subject<void>();
    cartService.onCartUpdated.and.returnValue(cartUpdateSubject.asObservable());

    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  // Lifecycle Methods
  describe('ngOnInit', () => {
    it('should call userService.getUserRole on initialization', () => {
      expect(userService.getUserRole).toHaveBeenCalled();
    });

    it('should call getCartCount on initialization', () => {
      spyOn(component, 'getCartCount');
      component.ngOnInit();
      expect(component.getCartCount).toHaveBeenCalled();
    });

    it('should call getCart on initialization', () => {
      spyOn(component, 'getCart');
      component.ngOnInit();
      expect(component.getCart).toHaveBeenCalled();
    });

    it('should subscribe to cartService.onCartUpdated', () => {
      expect(cartService.onCartUpdated).toHaveBeenCalled();
      expect(component['cartUpdateSubscription']).toBeDefined();
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from cartUpdateSubscription', () => {
      component['cartUpdateSubscription'] = jasmine.createSpyObj(
        'Subscription',
        ['unsubscribe'],
      );
      component.ngOnDestroy();
      expect(
        component['cartUpdateSubscription']?.unsubscribe,
      ).toHaveBeenCalled();
    });

    it('should not throw error if cartUpdateSubscription is undefined', () => {
      component['cartUpdateSubscription'] = undefined;
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  // Service Interactions
  describe('Service Interactions', () => {
    describe('getCartCount', () => {
      it('should update cartCount on successful cartService.getCart call', fakeAsync(() => {
        cartService.getCart.and.returnValue(of({ itemCount: 10 }));
        component.getCartCount();
        tick();
        expect(component.cartCount).toBe(10);
      }));

      it('should set cartCount to 0 on error in cartService.getCart', fakeAsync(() => {
        cartService.getCart.and.returnValue(
          throwError(() => new Error('Error')),
        );
        spyOn(console, 'error');
        component.cartCount = 0; // Reset cartCount
        component.getCartCount();
        tick();
        expect(component.cartCount).toBe(0);
        expect(console.error).toHaveBeenCalledWith(
          'Failed to fetch cart count:',
          jasmine.any(Error),
        );
      }));
    });

    describe('getCart', () => {
      it('should update sharedService.cartItems on successful cartService.getCart call', fakeAsync(() => {
        const cartItems = [
          { id: 1, name: 'Item 1' },
          { id: 2, name: 'Item 2' },
        ];
        cartService.getCart.and.returnValue(of({ items: cartItems }));
        component.getCart();
        tick();
        expect(sharedService.cartItems).toEqual(cartItems);
      }));

      it('should log error on cartService.getCart failure', fakeAsync(() => {
        cartService.getCart.and.returnValue(
          throwError(() => new Error('Cart error')),
        );
        spyOn(console, 'error');
        component.getCart();
        tick();
        expect(console.error).toHaveBeenCalledWith(
          'Cart error:',
          jasmine.any(Error),
        );
      }));
    });
  });

  // Template Rendering
  describe('Template Rendering', () => {
    it('should render top-bar when showHeader is true', () => {
      component.showHeader = true;
      fixture.detectChanges();
      const topBar = fixture.debugElement.query(By.css('.top-bar'));
      expect(topBar).toBeTruthy();
    });

    it('should not render top-bar when showHeader is false', () => {
      component.showHeader = false;
      fixture.detectChanges();
      const topBar = fixture.debugElement.query(By.css('.top-bar'));
      expect(topBar).toBeNull();
    });

    it('should not render cart icon for PLATFORM_ADMIN role', () => {
      userService.userInfo = { roleId: EnumUserRole.PLATFORM_ADMIN };
      fixture.detectChanges();
      const cartLink = fixture.debugElement.query(
        By.css('[data-testid="shoping-cart"]'),
      );
      expect(cartLink).toBeNull();
    });

    it('should render cart icon for non-PLATFORM_ADMIN role', () => {
      userService.userInfo = { roleId: EnumUserRole.INDEPENDEDNT_AGENT };
      fixture.detectChanges();
      const cartLink = fixture.debugElement.query(
        By.css('[data-testid="shoping-cart"]'),
      );
      expect(cartLink).toBeTruthy();
    });

    it('should display bell icon with badge', () => {
      component.cartCount = 3;
      fixture.detectChanges();
      const bellBadge = fixture.debugElement.queryAll(By.css('nz-badge'))[1];
      expect(bellBadge).toBeTruthy();
      expect(bellBadge.componentInstance.nzCount).toBe(3);
    });
  });

  // Event Handling
  describe('Event Handling', () => {
    it('should open drawer when cart icon is clicked', () => {
      userService.userInfo = { roleId: EnumUserRole.INDEPENDEDNT_AGENT };
      fixture.detectChanges();
      const cartLink = fixture.debugElement.query(
        By.css('[data-testid="shoping-cart"]'),
      );
      cartLink.triggerEventHandler('click', null);
      expect(component.visible).toBeTrue();
      fixture.detectChanges();
      const drawer = fixture.debugElement.query(By.css('nz-drawer'));
      expect(drawer.componentInstance.nzVisible).toBeTrue();
    });

    it('should close drawer when close button is clicked', () => {
      component.visible = true;
      fixture.detectChanges();
      const closeButton = fixture.debugElement.query(
        By.css('[data-testid="cart-close-drawer"]'),
      ).parent;
      closeButton?.triggerEventHandler('click', null);
      expect(component.visible).toBeFalse();
      fixture.detectChanges();
      const drawer = fixture.debugElement.query(By.css('nz-drawer'));
      expect(drawer.componentInstance.nzVisible).toBeFalse();
    });

    it('should close drawer when nzOnClose is emitted', () => {
      component.visible = true;
      fixture.detectChanges();
      const drawer = fixture.debugElement.query(By.css('nz-drawer'));
      drawer.triggerEventHandler('nzOnClose', null);
      fixture.detectChanges(); // Ensure view updates
      expect(component.visible).toBeFalse();
      expect(drawer.componentInstance.nzVisible).toBeFalse();
    });
  });

  // User Role
  describe('userRoleId', () => {
    it('should return user role ID from userService', () => {
      userService.userInfo = { roleId: EnumUserRole.INDEPENDEDNT_AGENT };
      expect(component.userRoleId).toBe(EnumUserRole.INDEPENDEDNT_AGENT);
    });

    it('should return 0 if userInfo is undefined', () => {
      userService.userInfo = undefined;
      expect(component.userRoleId).toBe(0);
    });
  });

  // Drawer Content
  describe('Drawer Content', () => {
    it('should render cart-item component in drawer', () => {
      component.visible = true;
      fixture.detectChanges();
      const cartItem = fixture.debugElement.query(
        By.directive(CartItemComponent),
      );
      expect(cartItem).toBeTruthy();
    });

    it('should display Order Summary title in drawer', fakeAsync(() => {
      component.visible = true; // Open the drawer
      fixture.detectChanges(); // Initial change detection
      tick(300); // Wait for drawer animation
      fixture.detectChanges(); // Update view after async operations
      let title = fixture.debugElement.query(By.css('nz-drawer h2'));
      if (!title) {
        title = fixture.debugElement.query(
          By.css('nz-drawer .ant-drawer-title h2'),
        );
        if (!title) {
          title = fixture.debugElement.query(By.css('.ant-drawer-title')); // NG-ZORRO title class
        }
      }
      expect(title).toBeTruthy('Drawer title element should exist');
      expect(title.nativeElement.textContent.trim()).toContain('Order Summary');
    }));
  });
});
