<div
  class="top-bar"
  *ngIf="showHeader && userRoleId !== enumUserRole.PLATFORM_ADMIN"
>
  <div class="header-icons">
    <a
      (click)="onClickWallet()"
      data-testid="wallet-link"
      class="wallet-link"
      *ngIf="userRoleId !== enumUserRole.PLATFORM_ADMIN"
    >
      <!-- 👇 Add balance display here -->

      <nz-badge
        [nzCount]="walletBalance"
        [nzSize]="'small'"
        [nzOffset]="[8, 0]"
      >
        <nz-icon nzType="wallet" nzTheme="fill" class="icon-lg" />
      </nz-badge>
    </a>
    <a
      (click)="onClickCart()"
      data-testid="shoping-cart"
      class="cart-link"
      *ngIf="userRoleId !== enumUserRole.PLATFORM_ADMIN"
    >
      <nz-badge [nzCount]="cartCount" [nzSize]="'small'" [nzOffset]="[8, 0]">
        <nz-icon
          nzType="shopping-cart"
          nzTheme="outline"
          class="icon-lg"
        ></nz-icon>
      </nz-badge>
    </a>

    <nz-badge [nzSize]="'small'" [nzOffset]="[8, 0]">
      <nz-icon nzType="notification" nzTheme="fill" class="icon-lg"></nz-icon>
    </nz-badge>
  </div>
</div>

<!-- Cart Drawer -->
<nz-drawer
  [nzClosable]="false"
  [nzVisible]="visible"
  nzPlacement="right"
  [nzTitle]="orderTitle"
  [nzExtra]="extra"
  (nzOnClose)="close()"
>
  <ng-container *nzDrawerContent>
    <app-cart-item
      [(visible)]="visible"
      (visibleAfterDelay)="visibleAfterDelay($event)"
    ></app-cart-item>
  </ng-container>
</nz-drawer>
<nz-drawer
  [nzClosable]="false"
  [nzVisible]="visibleWallet"
  nzPlacement="right"
  [nzTitle]="walletTitle"
  [nzExtra]="extraForWallet"
  (nzOnClose)="closeWalletDrawer()"
>
  <ng-container *nzDrawerContent>
    <app-wallet
      [(visibleWallet)]="visibleWallet"
      (visibleWalletAfterDelay)="visibleWalletAfterDelay($event)"
    ></app-wallet>
  </ng-container>
</nz-drawer>

<ng-template #walletTitle>
  <h2>Wallet</h2>
</ng-template>

<ng-template #orderTitle>
  <h2>Order Summary</h2>
</ng-template>

<!-- Extra close button inside cart drawer -->
<ng-template #extra>
  <button nz-button nzType="primary" nzGhost (click)="close()">
    <nz-icon
      data-testid="cart-close-drawer"
      nzType="close-circle"
      nzTheme="fill"
    />
    Close
  </button>
</ng-template>
<ng-template #extraForWallet>
  <button nz-button nzType="primary" nzGhost (click)="closeWalletDrawer()">
    <nz-icon
      data-testid="wallet-close-drawer"
      nzType="close-circle"
      nzTheme="fill"
    />
    Close
  </button>
</ng-template>
