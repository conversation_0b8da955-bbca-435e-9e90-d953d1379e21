import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import {
  Component,
  Input,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  ViewEncapsulation,
  inject,
} from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  faShoppingCart,
  faBell,
  faWallet,
} from '@fortawesome/free-solid-svg-icons';
import { NzBadgeModule } from 'ng-zorro-antd/badge';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';

import { CartService } from '../../core/services/cart.service';
import { SharedService } from '../../core/services/shared.service';
import { UserService } from '../../core/services/user.service';
import { NotificationService } from '../../core/services/notification.service';
import { RechargeService } from '../../core/services/recharge.service';
import { CartItemComponent } from '../cart-item/cart-item.component';
import { EnumUserRole } from '../../core/enumerations/user-roles';
import { COMMON_STRINGS } from '../../core/constants/common';
import { WalletComponent } from '../../core/features/wallet/wallet.component';

@Component({
  selector: 'app-top-bar',
  standalone: true,
  imports: [
    CommonModule,
    FontAwesomeModule,
    NzBadgeModule,
    NzIconModule,
    NzDrawerModule,
    NzButtonModule,
    CartItemComponent,
    WalletComponent,
  ],
  templateUrl: './top-bar.component.html',
  styleUrl: './top-bar.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class TopBarComponent implements OnInit, OnDestroy {
  @Input() showHeader = true;
  @Input() username = '';
  @Input() profileImageUrl: string | null = null;

  private cartService = inject(CartService);
  private sharedService = inject(SharedService);
  private userService = inject(UserService);
  private rechargeService = inject(RechargeService);
  private notificationService = inject(NotificationService);

  private cartUpdateSubscription?: Subscription;

  enumUserRole = EnumUserRole;
  cartCount = 0;
  walletBalance = 0;
  private userId?: number;
  faShoppingCart = faShoppingCart;
  faBell = faBell;
  faWallet = faWallet;
  visible = false;
  visibleWallet = false;

  ngOnInit(): void {
    this.userService.getUserRole();
    this.getCartCount();
    this.getCart();
    this.loadWalletBalance();

    this.cartUpdateSubscription = this.cartService
      .onCartUpdated()
      .subscribe(() => {
        this.getCartCount();
      });
  }

  ngOnDestroy(): void {
    this.cartUpdateSubscription?.unsubscribe();
  }

  getCartCount(): void {
    this.cartService.getCart().subscribe({
      next: (response) => {
        this.cartCount = response.itemCount || 0;
      },
      error: (err) => {
        console.error('Failed to fetch cart count:', err);
        this.cartCount = 0;
      },
    });
  }

  getCart(): void {
    this.cartService.getCart().subscribe({
      next: (response) => {
        this.sharedService.cartItems = response.items || [];
      },
      error: (err) => {
        console.error('Cart error:', err);
      },
    });
  }
  onClickWallet(): void {
    this.visibleWallet = true;
  }
  closeWallet(): void {
    this.visibleWallet = false;
  }

  onClickCart(): void {
    this.visible = true;
  }

  close(): void {
    this.visible = false;
  }
  closeWalletDrawer(): void {
    this.visibleWallet = false;
  }
  visibleWalletAfterDelay(duration: number): void {
    setTimeout(() => {
      this.visibleWallet = true;
    }, duration);
  }

  visibleAfterDelay(duration: number): void {
    setTimeout(() => {
      this.visible = true;
    }, duration);
  }

  get userRoleId(): number {
    return this.userService.userInfo?.roleId ?? 0;
  }
  private loadWalletBalance(): void {
    if (!this.userId) return;

    this.rechargeService.getUserWalletBalance(this.userId).subscribe({
      next: (balance) => (this.walletBalance = balance),
      error: () => {
        this.notificationService.error(
          COMMON_STRINGS.errorMessages.failedToFetchWalletBalance,
        );
      },
    });
  }
}
