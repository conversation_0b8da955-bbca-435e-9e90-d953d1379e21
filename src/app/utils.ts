import { IFormatDateOptions } from './core/interface/common';
import moment, { Moment } from 'moment';
import { parse, isValid } from 'date-fns';

export function formatDateField(
  dateString: Moment | Date | string | null,
  options: IFormatDateOptions = {},
) {
  const {
    separator = '/',
    ISODateFormat = false,
    dateFormat = 'ddMMyyyy',
    displayTime = false,
  } = options;
  if (!dateString || dateString === '0000-00-00 00:00:00') {
    return null;
  }

  // Check if the dateString is a moment object
  if (moment.isMoment(dateString)) {
    dateString = dateString.toISOString();
  }

  // Known formats to check first
  const formats = [
    'dd/MM/yy', // '31/7/24'
    'dd/MM/yyyy', // '31/7/2024'
    'dd-MM-yy', // '31-7-24'
    'dd-MM-yyyy', // '31-7-2024'

    'yyyy-MM-dd HH:mm:ss', // '2024-07-31 06:56:46'

    "yyyy-MM-dd'T'HH:mm:ss.SSSX", // ISO 8601 format with milliseconds ('2024-07-31T01:28:50.668Z')
    "yyyy-MM-dd'T'HH:mm:ssX", // ISO 8601 format without milliseconds ('2024-07-31T01:28:50Z')
  ];

  for (const format of formats) {
    const parsedDate = parse(dateString.toString(), format, new Date());
    if (isValid(parsedDate)) {
      return ISODateFormat ? parsedDate.toISOString() : formatDate(parsedDate);
    }
  }

  // Check for the format as 'Tue Jul 30 2024 19:49:14 GMT+0530 (India Standard Time)'
  const date = new Date(dateString as string);

  if (!isNaN(date.getTime())) {
    return ISODateFormat ? date.toISOString() : formatDate(date);
  }

  return null;

  function formatDate(date: Date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');
    switch (dateFormat) {
      case 'ddMMyyyy':
        return (
          [day, month, year].join(separator) +
          (displayTime ? ' ' + [hour, minute, second].join(':') : '')
        );
      case 'MMddyyyy':
        return (
          [month, day, year].join(separator) +
          (displayTime ? ' ' + [hour, minute, second].join(':') : '')
        );
      case 'yyyyMMdd':
        return (
          [year, month, day].join(separator) +
          (displayTime ? ' ' + [hour, minute, second].join(':') : '')
        );
    }
  }
}
