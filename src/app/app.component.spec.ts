// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { AppComponent } from './app.component';
// import { RouterTestingModule } from '@angular/router/testing';
// import { Router } from '@angular/router';
// import { provideHttpClientTesting } from '@angular/common/http/testing';
// import * as CryptoJS from 'crypto-js';

// describe('AppComponent', () => {
//   let component: AppComponent;
//   let fixture: ComponentFixture<AppComponent>;
//   let router: Router;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [RouterTestingModule],
//       providers: [provideHttpClientTesting()],
//     }).compileComponents();
//   });

//   beforeEach(() => {
//     fixture = TestBed.createComponent(AppComponent);
//     component = fixture.componentInstance;
//     router = TestBed.inject(Router);
//     fixture.detectChanges();
//   });

//   it('should create the app', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should navigate to login when navigateToLogin is called', () => {
//     const navigateSpy = spyOn(router, 'navigate');
//     component.navigateToLogin();
//     expect(navigateSpy).toHaveBeenCalledWith(['/login']);
//   });

//   it('should navigate to register when navigateToRegister is called', () => {
//     const navigateSpy = spyOn(router, 'navigate');
//     component.navigateToRegister();
//     expect(navigateSpy).toHaveBeenCalledWith(['/register']);
//   });

//   it('should encrypt and decrypt data using CryptoJS', () => {
//     const secretKey = 'test-secret';
//     const originalText = 'Hello, World!';

//     const encrypted = CryptoJS.AES.encrypt(originalText, secretKey).toString();
//     const decryptedBytes = CryptoJS.AES.decrypt(encrypted, secretKey);
//     const decryptedText = decryptedBytes.toString(CryptoJS.enc.Utf8);

//     expect(decryptedText).toEqual(originalText);
//   });

//   afterEach(() => {
//     fixture.destroy();
//   });
// });
