{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./out-tsc/app", "types": [], "module": "esnext", "moduleResolution": "node", "baseUrl": "./", "paths": {"*": ["src/*", "node_modules/*"]}}, "files": ["src/main.ts"], "include": ["src/**/*.ts"], "exclude": ["src/test.ts", "**/*.spec.ts", "src/environments/environment.ts", "src/environments/environment.prod.ts", "src/polyfills.ts", "src/app/core/constants/routes.ts", "src/app/core/guards/auth.guard.ts", "src/app/core/interceptors/auth.interceptor.ts", "src/app/core/models/api-response.model.ts", "src/app/core/models/user.model.ts", "src/app/core/services/auth.service.ts", "src/tests/**", "src/app/app-routing.module.ts"]}