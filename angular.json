{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"angular-ant-login-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/angular-ant-login-app", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "aot": true, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "uat": {"optimization": true, "outputHashing": "all", "sourceMap": true, "aot": true, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.uat.ts"}]}, "beta": {"optimization": true, "outputHashing": "all", "sourceMap": true, "aot": true, "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.beta.ts"}]}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}, "development": {"buildOptimizer": false, "optimization": false, "sourceMap": true, "namedChunks": true, "vendorChunk": true, "extractLicenses": false, "aot": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "angular-ant-login-app:build:production"}, "development": {"buildTarget": "angular-ant-login-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "angular-ant-login-app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "codeCoverage": true, "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/ng-zorro-antd/ng-zorro-antd.min.css"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "angular-ant-login-app:serve"}, "configurations": {"production": {"devServerTarget": "angular-ant-login-app:serve:production"}}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}}