#!/bin/bash

echo "Removing old API client..."
rm -rf ./src/app/api-client

npm install -g @openapitools/openapi-generator-cli

echo "Generating new API client from OpenAPI spec..."
openapi-generator-cli generate \
  -i https://api-areadocs-dev.arealytics.com.au/v3/api-docs \
  -g typescript-angular \
  --additional-properties=ngVersion=19.0.0,providedInRoot=true \
  -o ./src/app/api-client

echo "✅ API client updated successfully."
