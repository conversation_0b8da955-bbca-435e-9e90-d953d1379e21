{"name": "app-areadocs", "version": "1.0.0", "description": "This project was generated using [Angular CLI](https://github.com/angular/angular-cli) version 19.1.8.", "scripts": {"ng": "ng", "prestart": "npm run lint", "start": "ng serve", "build:dev": "ng build --configuration=development", "build:uat": "ng build --configuration=uat", "build:beta": "ng build --configuration=beta", "build:alpha": "ng build --configuration=alpha", "build:prod": "ng build --configuration=prod", "build:training": "ng build --configuration=training", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --watch=false --browsers=ChromeHeadless", "lint": "eslint .", "lint:fix": "ng lint --fix", "prepare": "husky install", "generate-api-client": "openapi-generator-cli generate -i https://api-areadocs-dev.arealytics.com.au/v3/api-docs -g typescript-angular -o src/app/api-client --additional-properties=providedInRoot=true", "update-api": "./update-api-client.sh"}, "private": true, "dependencies": {"@angular/animations": "^19.2.11", "@angular/cdk": "^19.2.16", "@angular/common": "^19.2.4", "@angular/compiler": "^19.2.4", "@angular/core": "^19.2.4", "@angular/forms": "^19.2.4", "@angular/material": "^19.2.16", "@angular/platform-browser": "^19.2.4", "@angular/platform-browser-dynamic": "^19.2.4", "@angular/router": "^19.2.4", "@ant-design/icons-angular": "^19.0.0", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@googlemaps/js-api-loader": "1.16.8", "bootstrap": "^5.3.3", "crypto-js": "^4.2.0", "keycloak-js": "^25.0.5", "lodash": "4.17.21", "ng-zorro-antd": "^19.2.2", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^11.1.0", "vite": "^6.2.4", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.5", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/cli": "^19.2.5", "@angular/compiler-cli": "^19.2.4", "@types/crypto-js": "^4.2.2", "@types/google.maps": "^3.58.1", "@types/jasmine": "~5.1.0", "@types/lodash": "4.17.16", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "angular-eslint": "19.3.0", "css-loader": "^7.1.2", "date-fns": "4.1.0", "eslint": "^9.23.0", "eslint-plugin-angular": "^4.1.0", "husky": "^8.0.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "karma-webpack": "^5.0.1", "lint-staged": "^15.5.0", "mini-css-extract-plugin": "^2.9.2", "moment": "2.30.1", "prettier": "^3.5.3", "typescript": "~5.5.4", "typescript-eslint": "8.27.0", "webpack": "^5.98.0"}, "lint-staged": {"**/*.{ts,js, mjs, html, json, scss}": "prettier --write", "**/*.{ts,js, mjs}": "eslint"}}