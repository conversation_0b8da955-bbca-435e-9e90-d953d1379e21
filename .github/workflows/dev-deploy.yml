name: Areadocs Dev Deployment 

on:
   workflow_dispatch

jobs:
  deploy:
    name: Deploying to Areadocs DEV
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}
          fetch-depth: 0
      - name: Get current deployment changes
        id: get-changes
        run: |
          # Get current branch
          BRANCH_NAME="develop"
          
          # Get the commit message of the latest commit
          CHANGES=$(git log -1 --pretty=format:"%s")
          
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT    
      - name: commands     
        run: |
          cd ./ &&
          npm install &&
          npm run build:dev &&
          cd ./dist
          zip -r dist.zip ./*
        continue-on-error: true

      - name: Copy files via ssh rsync
        uses: trendyminds/github-actions-rsync@master
        with:
          RSYNC_OPTIONS: -avzr 
          RSYNC_TARGET: /home/<USER>
          RSYNC_SOURCE: /dist/dist.zip
        env:
          SSH_PRIVATE_KEY: ${{ secrets.REMOTE_SSH_KEY }}
          SSH_HOSTNAME: ${{ secrets.REMOTE_HOST }}
          SSH_USERNAME: ${{ secrets.REMOTE_USER }}
          
      - name: Build & Deploy
        env:
          SSH_PRIVATE_KEY: ${{ secrets.DEV_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.DEV_HOSTNAME }}
          REMOTE_USER: ${{ secrets.DEV_USERNAME }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} '
          #sudo cp -rvfap /home/<USER>/dist /home/<USER>/dist/dist_bkp &&
          #sudo rm -rf /var/www/lit-dev.arealytics.com.au/dist/* &&
          sudo unzip -o -d "/home/<USER>/dist" /home/<USER>/dist.zip &&
          sudo chown -R ubuntu:ubuntu /home/<USER>
          sudo rm -rf /home/<USER>/browser.zip  
          '
  notify:
    name: Notify Slack for Deployment
    runs-on: ubuntu-latest
    needs: deploy
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Get current deployment changes
        id: get-changes
        run: |
          # Get current branch
          BRANCH_NAME="develop"
          
          # Get the commit message of the latest commit
          CHANGES=$(git log -1 --pretty=format:"%s")
          
          if [ -z "$CHANGES" ]; then
            CHANGES="No changes in this deployment"
          fi
          
          echo "changes<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      - name: Notify Slack
        uses: act10ns/slack@v2
        with:
          status: ${{ job.status }}
          message: |
            *${{ github.repository }} - #${{ github.run_number }}*
            Started by user ${{ github.actor }}
            *Deployment Status:* ${{ job.status }}
            *Changes in Current Deployment:* ${{ steps.get-changes.outputs.changes }}
            *Deployment URL:* https://areadocs-dev.arealytics.com.au
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()          
